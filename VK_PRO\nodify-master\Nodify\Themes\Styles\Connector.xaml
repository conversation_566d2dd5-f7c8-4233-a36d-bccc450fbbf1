﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <Style TargetType="{x:Type local:Connector}">
        <Setter Property="BorderBrush"
                Value="DodgerBlue" />
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Width"
                Value="14" />
        <Setter Property="Height"
                Value="14" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:Connector}">
                    <Ellipse x:Name="Connector"
                             Width="{TemplateBinding Width}"
                             Height="{TemplateBinding Height}"
                             Stroke="{TemplateBinding BorderBrush}"
                             Fill="Transparent"
                             StrokeThickness="2" />
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsConnected"
                                 Value="True">
                            <Setter TargetName="Connector"
                                    Property="Ellipse.Fill"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                        <Trigger Property="local:PendingConnection.IsOverElement"
                                 Value="True">
                            <Setter TargetName="Connector"
                                    Property="Ellipse.Fill"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:HotKeyControl}">
        <Setter Property="BorderBrush"
                Value="DodgerBlue" />
        <Setter Property="Background"
                Value="White" />
        <Setter Property="Foreground"
                Value="Black" />
        <Setter Property="Width"
                Value="16" />
        <Setter Property="Height"
                Value="16" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:HotKeyControl}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="3"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                        <TextBlock Text="{Binding Number, RelativeSource={RelativeSource TemplatedParent}}"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Center" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>