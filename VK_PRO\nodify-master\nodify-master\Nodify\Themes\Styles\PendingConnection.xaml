﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <Style TargetType="{x:Type local:PendingConnection}">
        <Setter Property="Background"
                Value="#121212" />
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="Stroke"
                Value="DodgerBlue" />
        <Setter Property="BorderBrush"
                Value="Black" />
        <Setter Property="EnablePreview"
                Value="False" />
        <Setter Property="StrokeThickness"
                Value="3" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="Visibility"
                Value="Hidden" />
        <Setter Property="StrokeDashArray"
                Value="4 4" />
        <Setter Property="Padding"
                Value="5" />
        <Setter Property="EnableSnapping"
                Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:PendingConnection}">
                    <Canvas>
                        <local:LineConnection Source="{TemplateBinding SourceAnchor}"
                                              Target="{TemplateBinding TargetAnchor}"
                                              Stroke="{TemplateBinding Stroke}"
                                              StrokeThickness="{TemplateBinding StrokeThickness}"
                                              StrokeDashArray="{TemplateBinding StrokeDashArray}"
                                              Direction="{TemplateBinding Direction}"
                                              Spacing="0"
                                              ArrowSize="0 0"
                                              SourceOffsetMode="None" 
                                              TargetOffsetMode="None" />
                        <Border Background="{TemplateBinding Background}"
                                Canvas.Left="{Binding TargetAnchor.X, RelativeSource={RelativeSource TemplatedParent}}"
                                Canvas.Top="{Binding TargetAnchor.Y, RelativeSource={RelativeSource TemplatedParent}}"
                                Visibility="{TemplateBinding EnablePreview, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Padding="{TemplateBinding Padding}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="3"
                                Margin="15">
                            <ContentPresenter />
                        </Border>
                    </Canvas>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>