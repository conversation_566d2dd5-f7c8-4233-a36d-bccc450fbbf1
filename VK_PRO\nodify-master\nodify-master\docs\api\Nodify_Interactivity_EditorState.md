# EditorState Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [EditorState](Nodify_Interactivity_EditorState)  
  
```csharp  
public static class EditorState  
```  
  
## Properties  
  
### AllowPanningWhileCutting  
  
Gets or sets a value indicating whether panning is allowed while cutting connections in the editor.  
  
```csharp  
public static bool AllowPanningWhileCutting { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### AllowPanningWhilePushingItems  
  
Gets or sets a value indicating whether panning is allowed while pushing items in the editor.  
  
```csharp  
public static bool AllowPanningWhilePushingItems { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### AllowPanningWhileSelecting  
  
Gets or sets a value indicating whether panning is allowed while selecting items in the editor.  
  
```csharp  
public static bool AllowPanningWhileSelecting { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### AllowZoomingWhileCutting  
  
Gets or sets a value indicating whether zooming is allowed while cutting connections in the editor.  
  
```csharp  
public static bool AllowZoomingWhileCutting { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### AllowZoomingWhilePanning  
  
Gets or sets a value indicating whether zooming is allowed while panning the editor viewport.  
  
```csharp  
public static bool AllowZoomingWhilePanning { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### AllowZoomingWhilePushingItems  
  
Gets or sets a value indicating whether zooming is allowed while pushing items in the editor.  
  
```csharp  
public static bool AllowZoomingWhilePushingItems { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### AllowZoomingWhileSelecting  
  
Gets or sets a value indicating whether zooming is allowed while selecting items in the editor.  
  
```csharp  
public static bool AllowZoomingWhileSelecting { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### EnableToggledCuttingMode  
  
Determines whether toggled cutting mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledCuttingMode { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### EnableToggledPanningMode  
  
Determines whether toggled panning mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledPanningMode { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### EnableToggledPushingItemsMode  
  
Determines whether toggled pushing items mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledPushingItemsMode { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### EnableToggledSelectingMode  
  
Determines whether toggled selecting mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledSelectingMode { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
