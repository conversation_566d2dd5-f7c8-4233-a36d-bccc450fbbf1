# EditorState.KeyboardNavigation Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<NodifyEditor\>](Nodify_Interactivity_InputElementState_TElement_) → [EditorState.KeyboardNavigation](Nodify_Interactivity_EditorState_KeyboardNavigation)  
  
**References:** [NodifyEditor](Nodify_NodifyEditor)  
  
```csharp  
public class KeyboardNavigation : InputElementState<NodifyEditor>  
```  
  
## Constructors  
  
### EditorState.KeyboardNavigation(NodifyEditor)  
  
```csharp  
public KeyboardNavigation(NodifyEditor element);  
```  
  
**Parameters**  
  
`element` [NodifyEditor](Nodify_NodifyEditor)  
  
## Methods  
  
### OnKeyDown(KeyEventArgs)  
  
```csharp  
protected override void OnKeyDown(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
### OnKeyUp(KeyEventArgs)  
  
```csharp  
protected override void OnKeyUp(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
