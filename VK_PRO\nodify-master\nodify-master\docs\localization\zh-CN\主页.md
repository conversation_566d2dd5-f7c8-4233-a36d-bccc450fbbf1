
![editor-interaction](https://user-images.githubusercontent.com/12727904/192004838-ec6dd997-5e64-4c01-940c-1cd1b8d27837.gif)


# 欢迎来到Nodify!

Nodify是一个WPF基于节点的[编辑器控件](编辑器概述)，其中包含一系列[节点](节点概述)、[连接](连接概述)和[连接器](连接器概述)组件，旨在简化构建基于节点的工具的过程。

这是受虚幻引擎的[蓝图视觉脚本](https://docs.unrealengine.com/en-US/ProgrammingAndScripting/Blueprints/index.html)系统启发，但仅专注于用户界面和用户交互部分。与蓝图不同，Nodify是一个通用库，提供了一个节点图编辑器组件，可以嵌入到任何 WPF 应用程序中。

该图形编辑器是一个无限区域，您可以在其中放置和移动节点，选择和拖动节点组，连接和断开节点或连接器，放大和缩小，以及在将节点或导线拖动到边缘附近时自动移动屏幕等。

Nodify功能丰富，经过优化，可以同时与数百个节点进行交互，并且...它是为与MVVM一起工作而重新架构的。

### 要求
![IDE](https://img.shields.io/static/v1?label=%20&message=VS%202019%20or%20greater&color=informational&style=for-the-badge&logo=visual-studio)

![C#](https://img.shields.io/static/v1?label=%20&message=8.0&color=239120&style=for-the-badge&logo=c-sharp)

![.NET](https://img.shields.io/static/v1?label=%20&message=Framework%204.7.2%20to%20NET%206&color=5C2D91&style=for-the-badge&logo=.net)

### 从Nuget安装Nodify
[![Download Package](https://img.shields.io/nuget/v/Nodify?label=Download&logo=nuget&style=for-the-badge)](https://www.nuget.org/packages/Nodify/)

```xml
Install-Package Nodify
```

### 应用案例
- [Playground](https://github.com/miroiu/nodify/tree/master/Examples/Nodify.Playground)
- [State machine](https://github.com/miroiu/nodify/tree/master/Examples/Nodify.StateMachine)
- [Calculator](https://github.com/miroiu/nodify/tree/master/Examples/Nodify.Calculator)


[![IDE](https://img.shields.io/static/v1?label=%20&message=GET%20STARTED&color=9cf&style=for-the-badge)](开始)

