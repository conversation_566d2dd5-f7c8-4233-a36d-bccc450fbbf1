﻿<UserControl x:Class="Nodify.Shapes.Canvas.CanvasView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Nodify.Shapes.Canvas"
             xmlns:nodify="https://miroiu.github.io/nodify"
             xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
             xmlns:controls="clr-namespace:Nodify.Shapes.Controls"
             xmlns:o="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
             mc:Ignorable="d"
             Background="#0a172a"
             Foreground="white"
             d:DataContext="{d:DesignInstance Type=local:CanvasViewModel, IsDesignTimeCreatable=True}"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <Grid>
        <Grid.Resources>
            <shared:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <shared:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <shared:ColorToSolidColorBrushConverter x:Key="ColorToSolidColorBrushConverter" />

            <VisualBrush x:Key="GridDrawingBrush"
                         TileMode="Tile"
                         Viewport="0 0 30 30"
                         ViewportUnits="Absolute"
                         Viewbox="0 0 30 30"
                         ViewboxUnits="Absolute"
                         o:Freeze="True"
                         Transform="{Binding ViewportTransform, ElementName=Editor}">
                <VisualBrush.Visual>
                    <Rectangle Width="1"
                               Height="1"
                               Fill="White" />
                </VisualBrush.Visual>
            </VisualBrush>
        </Grid.Resources>

        <nodify:NodifyEditor ItemsSource="{Binding Shapes}"
                             SelectedItems="{Binding SelectedShapes}"
                             Connections="{Binding Connections}"
                             Decorators="{Binding Decorators}"
                             Background="{StaticResource GridDrawingBrush}"
                             ConnectionCompletedCommand="{Binding CreateConnectionCommand}"
                             ItemsDragStartedCommand="{Binding MoveShapesStartedCommand}"
                             ItemsDragCompletedCommand="{Binding MoveShapesCompletedCommand}"
                             RemoveConnectionCommand="{Binding RemoveConnectionCommand}"
                             SelectionChanged="Editor_SelectionChanged"
                             ItemsMoved="Editor_ItemsMoved"
                             MouseDown="Editor_MouseDown"
                             MouseMove="Editor_MouseMove"
                             MouseUp="Editor_MouseUp"
                             GridCellSize="5"
                             x:Name="Editor">
            <nodify:NodifyEditor.Style>
                <Style TargetType="{x:Type nodify:NodifyEditor}"
                       BasedOn="{StaticResource {x:Type nodify:NodifyEditor}}">
                    <Setter Property="Cursor"
                            Value="Cross" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding CanvasToolbar.SelectedTool}"
                                     Value="{x:Static local:CanvasTool.None}">
                            <Setter Property="Cursor"
                                    Value="Arrow" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </nodify:NodifyEditor.Style>

            <nodify:NodifyEditor.Resources>
                <DataTemplate DataType="{x:Type local:EllipseViewModel}">
                    <Ellipse Stretch="Fill"
                             Focusable="False"
                             Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                             Stroke="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                             StrokeThickness="2"
                             Opacity="0.8" />
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:RectangleViewModel}">
                    <Rectangle Stretch="Fill"
                               Focusable="False"
                               Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                               Stroke="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                               StrokeThickness="2"
                               Opacity="0.8" />
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:TriangleViewModel}">
                    <Polygon Points="0,100 50,0 100,100"
                             Focusable="False"
                             Stretch="Fill"
                             Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                             Stroke="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                             StrokeThickness="2"
                             Opacity="0.8" />
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:ShapeToolbarViewModel}">
                    <Canvas Visibility="{Binding Shape, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <shared:Swatches SelectedColor="{Binding Shape.Color}"
                                         Colors="{x:Static local:ShapeViewModel.Colors}"
                                         IsEnabled="{Binding DataContext.CanvasToolbar.Locked, ElementName=Editor, Converter={StaticResource InverseBooleanConverter}}"
                                         Canvas.Top="-70"
                                         Panel.ZIndex="1">
                            <shared:Swatches.Effect>
                                <DropShadowEffect ShadowDepth="1" />
                            </shared:Swatches.Effect>
                        </shared:Swatches>
                    </Canvas>
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:UserCursorViewModel}">
                    <StackPanel IsHitTestVisible="False">
                        <Viewbox Width="24"
                                 Height="24"
                                 Margin="-10 0 0 5"
                                 Stretch="Fill"
                                 HorizontalAlignment="Left">
                            <Path Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                  Stroke="White"
                                  StrokeLineJoin="Round"
                                  StrokeStartLineCap="Round"
                                  StrokeEndLineCap="Round"
                                  Data="M.256.255a.874.874 0 0 0-.18.974l4.753 17.114a.875.875 0 0 0 1.603-.012L10 10l8.334-3.57a.875.875 0 0 0 .01-1.601L1.23.075a.874.874 0 0 0-.974.18Z" />
                        </Viewbox>
                        <Border CornerRadius="3"
                                Background="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}, ConverterParameter=0.7}"
                                Padding="6 2">
                            <TextBlock Text="{Binding Name}" />
                        </Border>
                    </StackPanel>
                </DataTemplate>

                <Style TargetType="{x:Type nodify:PendingConnection}"
                       BasedOn="{StaticResource {x:Type nodify:PendingConnection}}">
                    <Setter Property="Stroke">
                        <Setter.Value>
                            <SolidColorBrush Color="White"
                                             Opacity="0.7" />
                        </Setter.Value>
                    </Setter>
                </Style>
            </nodify:NodifyEditor.Resources>

            <nodify:NodifyEditor.ConnectionTemplate>
                <DataTemplate DataType="{x:Type local:ConnectionViewModel}">
                    <nodify:StepConnection Source="{Binding Source.Anchor}"
                                           Target="{Binding Target.Anchor}"
                                           SourcePosition="{Binding Source.Position}"
                                           TargetPosition="{Binding Target.Position}"
                                           Cursor="Hand"
                                           IsSelectable="True"
                                           Fill="Transparent"
                                           FocusVisualPadding="2"
                                           StrokeThickness="3">
                        <nodify:StepConnection.Stroke>
                            <SolidColorBrush Color="White"
                                             Opacity="0.7" />
                        </nodify:StepConnection.Stroke>
                        <nodify:StepConnection.Style>
                            <Style TargetType="{x:Type nodify:StepConnection}">
                                <Setter Property="OutlineBrush"
                                        Value="Transparent" />
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver"
                                             Value="True">
                                        <Setter Property="OutlineBrush">
                                            <Setter.Value>
                                                <SolidColorBrush Color="White"
                                                                 Opacity="0.15" />
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger Property="IsSelected"
                                             Value="True">
                                        <Setter Property="OutlineBrush">
                                            <Setter.Value>
                                                <SolidColorBrush Color="DodgerBlue"
                                                                 Opacity="0.25" />
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </nodify:StepConnection.Style>
                    </nodify:StepConnection>
                </DataTemplate>
            </nodify:NodifyEditor.ConnectionTemplate>

            <nodify:NodifyEditor.DecoratorContainerStyle>
                <Style TargetType="{x:Type nodify:DecoratorContainer}"
                       BasedOn="{StaticResource {x:Type nodify:DecoratorContainer}}">
                    <Setter Property="Location"
                            Value="{Binding Location}" />
                </Style>
            </nodify:NodifyEditor.DecoratorContainerStyle>

            <nodify:NodifyEditor.ItemContainerStyle>
                <Style TargetType="{x:Type nodify:ItemContainer}"
                       BasedOn="{StaticResource {x:Type nodify:ItemContainer}}">
                    <Style.Resources>
                        <Style TargetType="{x:Type nodify:Connector}"
                               BasedOn="{StaticResource {x:Type nodify:Connector}}">
                            <Setter Property="BorderBrush"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource AncestorType=nodify:ItemContainer}}" />
                            <Setter Property="Background"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource AncestorType=nodify:ItemContainer}}" />
                            <Setter Property="HorizontalContentAlignment"
                                    Value="Center" />
                            <Setter Property="VerticalContentAlignment"
                                    Value="Center" />
                            <Setter Property="IsConnected"
                                    Value="True" />
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <Grid Background="Transparent">
                                            <Ellipse x:Name="PART_Connector"
                                                     Width="14"
                                                     Height="14"
                                                     Stroke="{TemplateBinding BorderBrush}"
                                                     Fill="{TemplateBinding Background}"
                                                     HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                     VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                                        </Grid>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>

                        <Style TargetType="{x:Type shared:Resizer}"
                               BasedOn="{StaticResource {x:Type shared:Resizer}}">
                            <Setter Property="Background"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource AncestorType=nodify:ItemContainer}}" />
                            <Setter Property="IsEnabled"
                                    Value="{Binding DataContext.CanvasToolbar.Locked, ElementName=Editor, Converter={StaticResource InverseBooleanConverter}}" />
                        </Style>

                        <!--For some reason, the designer doesn't like having these bindings on the element itself but in a style-->
                        <Style TargetType="{x:Type controls:ResizableContainer}"
                               BasedOn="{StaticResource {x:Type shared:ResizablePanel}}">
                            <Setter Property="BorderBrush"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource AncestorType=nodify:ItemContainer}}" />
                            <Setter Property="Padding"
                                    Value="3" />
                            <Setter Property="ResizeStartedCommand"
                                    Value="{Binding DataContext.ResizeShapeStartedCommand, ElementName=Editor}" />
                            <Setter Property="ResizeCompletedCommand"
                                    Value="{Binding DataContext.ResizeShapeCompletedCommand, ElementName=Editor}" />
                        </Style>
                    </Style.Resources>
                    <Setter Property="Location"
                            Value="{Binding Location}" />
                    <Setter Property="SelectedBrush"
                            Value="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}" />
                    <Setter Property="SelectedBorderThickness"
                            Value="1" />
                    <Setter Property="BorderBrush"
                            Value="Transparent" />
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="{x:Type nodify:ItemContainer}">
                                <controls:ResizableContainer Height="{Binding Height, Mode=TwoWay}"
                                                             Width="{Binding Width, Mode=TwoWay}"
                                                             Directions="Corners"
                                                             GridCellSize="5"
                                                             Focusable="False">
                                    <Grid>
                                        <ContentPresenter Cursor="Hand" />

                                        <shared:EditableTextBlock Text="{Binding Text}"
                                                                  IsEnabled="{Binding DataContext.CanvasToolbar.Locked, ElementName=Editor, Converter={StaticResource InverseBooleanConverter}}"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalContentAlignment="Center"
                                                                  FontSize="16"
                                                                  ToolTip="Double click to edit" />

                                        <DockPanel LastChildFill="False">
                                            <nodify:Connector DataContext="{Binding LeftConnector}"
                                                              Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                              DockPanel.Dock="Left"
                                                              HorizontalContentAlignment="Left"
                                                              Margin="-11 0 0 0"
                                                              Visibility="Hidden"
                                                              Height="Auto"
                                                              Width="25"
                                                              x:Name="LeftConnector" />

                                            <nodify:Connector DataContext="{Binding RightConnector}"
                                                              Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                              DockPanel.Dock="Right"
                                                              HorizontalContentAlignment="Right"
                                                              Margin="0 0 -11 0"
                                                              Visibility="Hidden"
                                                              Height="Auto"
                                                              Width="25"
                                                              x:Name="RightConnector" />

                                            <nodify:Connector DataContext="{Binding TopConnector}"
                                                              Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                              DockPanel.Dock="Top"
                                                              VerticalContentAlignment="Top"
                                                              Margin="0 -11 0 0"
                                                              Visibility="Hidden"
                                                              Width="Auto"
                                                              Height="25"
                                                              x:Name="TopConnector" />

                                            <nodify:Connector DataContext="{Binding BottomConnector}"
                                                              Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                              DockPanel.Dock="Bottom"
                                                              VerticalContentAlignment="Bottom"
                                                              Margin="0 0 0 -11"
                                                              Visibility="Hidden"
                                                              Width="Auto"
                                                              Height="25"
                                                              x:Name="BottomConnector" />
                                        </DockPanel>
                                    </Grid>
                                </controls:ResizableContainer>

                                <ControlTemplate.Triggers>
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected"
                                                       Value="True" />
                                            <Condition Property="IsPreviewingSelection"
                                                       Value="{x:Null}" />
                                        </MultiTrigger.Conditions>
                                        <MultiTrigger.Setters>
                                            <Setter Property="Visibility"
                                                    TargetName="LeftConnector"
                                                    Value="Visible" />
                                            <Setter Property="Visibility"
                                                    TargetName="RightConnector"
                                                    Value="Visible" />
                                            <Setter Property="Visibility"
                                                    TargetName="TopConnector"
                                                    Value="Visible" />
                                            <Setter Property="Visibility"
                                                    TargetName="BottomConnector"
                                                    Value="Visible" />
                                        </MultiTrigger.Setters>
                                    </MultiTrigger>
                                    <Trigger Property="IsPreviewingSelection"
                                             Value="True">
                                        <Setter Property="Visibility"
                                                TargetName="LeftConnector"
                                                Value="Visible" />
                                        <Setter Property="Visibility"
                                                TargetName="RightConnector"
                                                Value="Visible" />
                                        <Setter Property="Visibility"
                                                TargetName="TopConnector"
                                                Value="Visible" />
                                        <Setter Property="Visibility"
                                                TargetName="BottomConnector"
                                                Value="Visible" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <Trigger Property="IsSelected"
                                 Value="True">
                            <Setter Property="Panel.ZIndex"
                                    Value="1" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </nodify:NodifyEditor.ItemContainerStyle>
        </nodify:NodifyEditor>

        <!--MINIMAP-->
        <shared:ResizablePanel Directions="BottomLeft"
                               VerticalAlignment="Top"
                               HorizontalAlignment="Right"
                               Width="300"
                               Height="200"
                               MinWidth="250"
                               MinHeight="150"
                               BorderBrush="{x:Null}"
                               Focusable="False"
                               Margin="20">
            <shared:ResizablePanel.Resources>
                <Style TargetType="{x:Type shared:Resizer}">
                    <Setter Property="Cursor"
                            Value="SizeNESW" />
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="{x:Type shared:Resizer}">
                                <Grid Margin="-3 -6 -6 -3"
                                      Background="Transparent">
                                    <Rectangle Height="12"
                                               Width="3"
                                               Fill="#d2d4d7"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Bottom" />
                                    <Rectangle Height="3"
                                               Width="12"
                                               Fill="#d2d4d7"
                                               VerticalAlignment="Bottom"
                                               HorizontalAlignment="Left" />
                                </Grid>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </shared:ResizablePanel.Resources>
            <Border CornerRadius="3"
                    BorderBrush="#1e293b"
                    BorderThickness="3">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="1" />
                </Border.Effect>

                <nodify:Minimap ItemsSource="{Binding ItemsSource, ElementName=Editor}"
                                ViewportLocation="{Binding ViewportLocation, ElementName=Editor}"
                                ViewportSize="{Binding ViewportSize, ElementName=Editor}"
                                ResizeToViewport="True"
                                Zoom="Minimap_Zoom">
                    <nodify:Minimap.Resources>
                        <DataTemplate DataType="{x:Type local:EllipseViewModel}">
                            <Ellipse Stretch="Fill"
                                     Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                     Stroke="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                     StrokeThickness="2"
                                     Opacity="0.8" />
                        </DataTemplate>

                        <DataTemplate DataType="{x:Type local:RectangleViewModel}">
                            <Rectangle Stretch="Fill"
                                       Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                       Stroke="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                       StrokeThickness="2"
                                       Opacity="0.8" />
                        </DataTemplate>

                        <DataTemplate DataType="{x:Type local:TriangleViewModel}">
                            <Polygon Points="0,100 50,0 100,100"
                                     Stretch="Fill"
                                     Fill="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                     Stroke="{Binding BorderColor, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                     StrokeThickness="2"
                                     Opacity="0.8" />
                        </DataTemplate>
                    </nodify:Minimap.Resources>
                    <nodify:Minimap.Background>
                        <SolidColorBrush Color="#111a2d"
                                         Opacity="0.5" />
                    </nodify:Minimap.Background>
                    <nodify:Minimap.ItemContainerStyle>
                        <Style TargetType="{x:Type nodify:MinimapItem}">
                            <Setter Property="Location"
                                    Value="{Binding Location}" />
                            <Setter Property="Width"
                                    Value="{Binding Width}" />
                            <Setter Property="Height"
                                    Value="{Binding Height}" />
                        </Style>
                    </nodify:Minimap.ItemContainerStyle>
                    <nodify:Minimap.ViewportStyle>
                        <Style TargetType="Rectangle">
                            <Setter Property="StrokeThickness"
                                    Value="3" />
                            <Setter Property="Fill">
                                <Setter.Value>
                                    <SolidColorBrush Color="#445e87"
                                                     Opacity="0.3" />
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </nodify:Minimap.ViewportStyle>
                </nodify:Minimap>
            </Border>
        </shared:ResizablePanel>

        <!--TOOLBARS-->

        <Border VerticalAlignment="Bottom"
                HorizontalAlignment="Center"
                Margin="0 0 0 20"
                CornerRadius="3"
                Background="#1e293b">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" />
            </Border.Effect>
            <ItemsControl Focusable="True"
                          KeyboardNavigation.TabNavigation="Cycle">
                <ItemsControl.Resources>
                    <Style TargetType="Button"
                           BasedOn="{StaticResource IconButton}">
                        <Setter Property="Margin"
                                Value="2" />
                        <Setter Property="Width"
                                Value="32" />
                        <Setter Property="Height"
                                Value="32" />
                        <Setter Property="Focusable"
                                Value="False" />
                    </Style>
                </ItemsControl.Resources>

                <ListBox BorderThickness="0"
                         Background="Transparent"
                         ItemsSource="{x:Static local:CanvasToolbarViewModel.AvailableTools}"
                         SelectedValue="{Binding CanvasToolbar.SelectedTool}"
                         KeyboardNavigation.ControlTabNavigation="None">
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Margin"
                                    Value="2" />
                            <Setter Property="Padding"
                                    Value="0" />
                            <Setter Property="Width"
                                    Value="32" />
                            <Setter Property="Height"
                                    Value="32" />
                        </Style>
                    </ListBox.ItemContainerStyle>

                    <ListBox.Resources>
                        <DataTemplate x:Key="Cursor">
                            <Border CornerRadius="3"
                                    Padding="5">
                                <ContentPresenter ContentTemplate="{StaticResource CursorIcon}" />
                            </Border>
                        </DataTemplate>
                        <DataTemplate x:Key="Circle">
                            <Border CornerRadius="3"
                                    Padding="5">
                                <ContentPresenter ContentTemplate="{StaticResource CircleIcon}" />
                            </Border>
                        </DataTemplate>
                        <DataTemplate x:Key="Square">
                            <Border CornerRadius="3"
                                    Padding="5">
                                <ContentPresenter ContentTemplate="{StaticResource SquareIcon}" />
                            </Border>
                        </DataTemplate>
                        <DataTemplate x:Key="Triangle">
                            <Border CornerRadius="3"
                                    Padding="5">
                                <ContentPresenter ContentTemplate="{StaticResource TriangleIcon}" />
                            </Border>
                        </DataTemplate>
                    </ListBox.Resources>

                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <ContentControl Content="{Binding}"
                                            Focusable="False">
                                <ContentControl.Style>
                                    <Style TargetType="ContentControl">
                                        <Setter Property="ContentTemplate"
                                                Value="{StaticResource Cursor}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding}"
                                                         Value="{x:Static local:CanvasTool.Ellipse}">
                                                <Setter Property="ContentTemplate"
                                                        Value="{StaticResource Circle}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding}"
                                                         Value="{x:Static local:CanvasTool.Rectangle}">
                                                <Setter Property="ContentTemplate"
                                                        Value="{StaticResource Square}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding}"
                                                         Value="{x:Static local:CanvasTool.Triangle}">
                                                <Setter Property="ContentTemplate"
                                                        Value="{StaticResource Triangle}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ContentControl.Style>
                            </ContentControl>
                        </DataTemplate>
                    </ListBox.ItemTemplate>

                    <ListBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal"
                                        IsItemsHost="True" />
                        </ItemsPanelTemplate>
                    </ListBox.ItemsPanel>
                </ListBox>

                <Button Command="{Binding UndoCommand}"
                        CommandTarget="{Binding ElementName=Editor}"
                        ContentTemplate="{StaticResource ArrowBackIcon}"
                        ToolTip="CTRL+Z"
                        Padding="1" />

                <Button Command="{Binding RedoCommand}"
                        CommandTarget="{Binding ElementName=Editor}"
                        ContentTemplate="{StaticResource ArrowForwardIcon}"
                        ToolTip="CTRL+SHIFT+z"
                        Padding="1" />

                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>

            </ItemsControl>
        </Border>

        <Border VerticalAlignment="Bottom"
                HorizontalAlignment="Left"
                Margin="20 0 0 20"
                CornerRadius="3"
                Background="#1e293b">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" />
            </Border.Effect>

            <ItemsControl Focusable="True"
                          KeyboardNavigation.TabNavigation="Cycle"
                          KeyboardNavigation.DirectionalNavigation="Cycle"
                          KeyboardNavigation.ControlTabNavigation="None">
                <ItemsControl.Resources>
                    <Style TargetType="Button"
                           BasedOn="{StaticResource IconButton}">
                        <Setter Property="Margin"
                                Value="2" />
                        <Setter Property="Width"
                                Value="32" />
                        <Setter Property="Height"
                                Value="32" />
                    </Style>
                </ItemsControl.Resources>

                <Button Command="{x:Static nodify:EditorCommands.ZoomIn}"
                        CommandTarget="{Binding ElementName=Editor}"
                        ContentTemplate="{StaticResource PlusIcon}" />

                <Button Command="{x:Static nodify:EditorCommands.ZoomOut}"
                        CommandTarget="{Binding ElementName=Editor}"
                        ContentTemplate="{StaticResource MinusIcon}" />

                <Button Command="{x:Static nodify:EditorCommands.FitToScreen}"
                        CommandTarget="{Binding ElementName=Editor}"
                        ContentTemplate="{StaticResource MaximizeIcon}" />

                <Button Command="{Binding ToggleLockCommand}"
                        DataContext="{Binding CanvasToolbar}">
                    <Button.Style>
                        <Style TargetType="Button"
                               BasedOn="{StaticResource {x:Type Button}}">
                            <Setter Property="ContentTemplate"
                                    Value="{StaticResource LockOpenIcon}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Locked}"
                                             Value="True">
                                    <Setter Property="ContentTemplate"
                                            Value="{StaticResource LockIcon}" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </ItemsControl>
        </Border>

        <Border VerticalAlignment="Bottom"
                HorizontalAlignment="Right"
                Margin="0 0 20 20"
                Height="38"
                CornerRadius="3">

            <ItemsControl ItemsSource="{Binding Cursors}"
                          Focusable="True"
                          KeyboardNavigation.TabNavigation="Cycle"
                          KeyboardNavigation.ControlTabNavigation="None">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>

                <ItemsControl.ItemTemplate>
                    <DataTemplate DataType="{x:Type local:UserCursorViewModel}">
                        <Border CornerRadius="50"
                                Height="28"
                                Width="28"
                                Margin="-3"
                                Background="{Binding Color, Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                Padding="6 2">
                            <Border.Effect>
                                <DropShadowEffect ShadowDepth="1" />
                            </Border.Effect>
                            <Button Command="{x:Static nodify:EditorCommands.BringIntoView}"
                                    CommandParameter="{Binding Location}"
                                    CommandTarget="{Binding ElementName=Editor}"
                                    Style="{StaticResource IconButton}"
                                    ToolTip="{Binding Name}"
                                    Content="{Binding Name[0]}"
                                    Foreground="White"
                                    Margin="-6 -2" />
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Border>
    </Grid>
</UserControl>
