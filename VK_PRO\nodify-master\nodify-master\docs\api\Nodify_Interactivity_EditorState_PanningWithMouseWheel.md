# EditorState.PanningWithMouseWheel Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<NodifyEditor\>](Nodify_Interactivity_InputElementState_TElement_) → [EditorState.PanningWithMouseWheel](Nodify_Interactivity_EditorState_PanningWithMouseWheel)  
  
**References:** [NodifyEditor](Nodify_NodifyEditor)  
  
```csharp  
public class PanningWithMouseWheel : InputElementState<NodifyEditor>  
```  
  
## Constructors  
  
### EditorState.PanningWithMouseWheel(NodifyEditor)  
  
```csharp  
public PanningWithMouseWheel(NodifyEditor editor);  
```  
  
**Parameters**  
  
`editor` [NodifyEditor](Nodify_NodifyEditor)  
  
## Methods  
  
### OnMouseWheel(MouseWheelEventArgs)  
  
```csharp  
protected override void OnMouseWheel(MouseWheelEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseWheelEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseWheelEventArgs)  
  
