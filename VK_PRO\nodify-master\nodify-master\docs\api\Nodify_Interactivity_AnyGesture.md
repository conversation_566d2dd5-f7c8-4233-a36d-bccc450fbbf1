# AnyGesture Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputGesture](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.InputGesture) → [MultiGesture](Nodify_Interactivity_MultiGesture) → [AnyGesture](Nodify_Interactivity_AnyGesture)  
  
```csharp  
public sealed class AnyGesture : MultiGesture  
```  
  
## Constructors  
  
### AnyGesture(InputGesture[])  
  
```csharp  
public AnyGesture(InputGesture[] gestures);  
```  
  
**Parameters**  
  
`gestures` [InputGesture[]](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.InputGesture[])  
  
