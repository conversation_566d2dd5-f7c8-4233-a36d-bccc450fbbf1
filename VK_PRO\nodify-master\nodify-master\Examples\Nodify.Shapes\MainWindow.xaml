﻿<Window x:Class="Nodify.Shapes.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:o="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
        xmlns:local="clr-namespace:Nodify.Shapes"
        xmlns:canvas="clr-namespace:Nodify.Shapes.Canvas"
        mc:Ignorable="d"
        xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
        Title="Canvas"
        Height="650"
        Width="1200">
    <Window.InputBindings>
        <KeyBinding Command="{Binding Canvas.RedoCommand}"
                    Key="Y"
                    Modifiers="Ctrl" />
        <KeyBinding Command="{Binding Canvas.RedoCommand}"
                    Key="Z"
                    Modifiers="Ctrl+Shift" />
        <KeyBinding Command="{Binding Canvas.UndoCommand}"
                    Key="Z"
                    Modifiers="Ctrl" />
        <KeyBinding Command="{Binding Canvas.DeleteSelectionCommand}"
                    Key="Delete" />
    </Window.InputBindings>

    <Window.DataContext>
        <local:AppShellViewModel />
    </Window.DataContext>

    <Grid>
        <canvas:CanvasView DataContext="{Binding Canvas}" />
    </Grid>
</Window>
