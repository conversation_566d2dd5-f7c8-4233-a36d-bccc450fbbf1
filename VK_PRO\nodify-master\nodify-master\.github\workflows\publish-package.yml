name: Publish package

on:
  workflow_dispatch:
  push:
    tags:
      - "v*.*.*"

jobs:
  publish:
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET Core
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"
      - name: Install dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --configuration Release --no-restore
      - name: Publish the package
        run: dotnet nuget push "*/bin/Release/*.nupkg" -k ${{ secrets.NUGET_KEY }} -s https://api.nuget.org/v3/index.json
