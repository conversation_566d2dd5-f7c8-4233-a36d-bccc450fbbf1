# HotKeysDisplayMode Enum  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**References:** [PendingConnection](Nodify_PendingConnection)  
  
Specifies how hotkeys are displayed for a pending connection.  
  
```csharp  
public enum HotKeysDisplayMode  
```  
  
## Fields  
  
### All  
  
Display hotkeys for both mouse and keyboard.  
  
```csharp  
All = 2;  
```  
  
### Keyboard  
  
Display hotkeys for keyboard only.  
  
```csharp  
Keyboard = 1;  
```  
  
### None  
  
No hotkeys will be displayed for the pending connection.  
  
```csharp  
None = 0;  
```  
  
