# InputGestureRefExtensions Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputGestureRefExtensions](Nodify_Interactivity_InputGestureRefExtensions)  
  
**References:** [InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
Extension methods for the [InputGestureRef](Nodify_Interactivity_InputGestureRef) class.  
  
```csharp  
public static class InputGestureRefExtensions  
```  
  
## Methods  
  
### AsRef(InputGesture)  
  
Creates a new [InputGestureRef](Nodify_Interactivity_InputGestureRef) from the specified gesture.  
  
```csharp  
public static InputGestureRef AsRef(InputGesture gesture);  
```  
  
**Parameters**  
  
`gesture` [InputGesture](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.InputGesture)  
  
**Returns**  
  
[InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
