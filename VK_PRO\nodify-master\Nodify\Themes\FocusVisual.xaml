﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:o="http://schemas.microsoft.com/winfx/2006/xaml/presentation/options"
                    xmlns:local="clr-namespace:Nodify">

    <Color x:Key="NodifyEditor.FocusVisualColor">#FD5618</Color>

    <SolidColorBrush x:Key="NodifyEditor.FocusVisualBrush"
                     o:Freeze="True"
                     Color="{DynamicResource NodifyEditor.FocusVisualColor}" />

    <Style x:Key="EditorFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle StrokeThickness="1"
                               StrokeDashArray="2"
                               Margin="-2"
                               RadiusX="3"
                               RadiusY="3"
                               Stroke="{StaticResource NodifyEditor.FocusVisualBrush}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:NodifyEditor}"
           BasedOn="{StaticResource {x:Type local:NodifyEditor}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Style TargetType="{x:Type local:ItemContainer}"
           BasedOn="{StaticResource {x:Type local:ItemContainer}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Style TargetType="{x:Type local:DecoratorContainer}"
           BasedOn="{StaticResource {x:Type local:DecoratorContainer}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Style TargetType="{x:Type local:Connector}"
           BasedOn="{StaticResource {x:Type local:Connector}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Style TargetType="{x:Type local:NodeInput}"
           BasedOn="{StaticResource {x:Type local:NodeInput}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Style TargetType="{x:Type local:NodeOutput}"
           BasedOn="{StaticResource {x:Type local:NodeOutput}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Style TargetType="{x:Type local:Minimap}"
           BasedOn="{StaticResource {x:Type local:Minimap}}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource EditorFocusVisualStyle}" />
    </Style>

    <Pen x:Key="{x:Static local:BaseConnection.FocusVisualPenKey}"
         Thickness="1"
         Brush="{StaticResource NodifyEditor.FocusVisualBrush}">
        <Pen.DashStyle>
            <DashStyle Dashes="0.5,3" />
        </Pen.DashStyle>
    </Pen>

</ResourceDictionary>