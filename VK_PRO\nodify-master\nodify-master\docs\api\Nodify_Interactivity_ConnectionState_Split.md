# ConnectionState.Split Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<BaseConnection\>](Nodify_Interactivity_InputElementState_TElement_) → [ConnectionState.Split](Nodify_Interactivity_ConnectionState_Split)  
  
**References:** [BaseConnection](Nodify_BaseConnection)  
  
```csharp  
public class Split : InputElementState<BaseConnection>  
```  
  
## Constructors  
  
### ConnectionState.Split(BaseConnection)  
  
```csharp  
public Split(BaseConnection connection);  
```  
  
**Parameters**  
  
`connection` [BaseConnection](Nodify_BaseConnection)  
  
## Methods  
  
### OnMouseDown(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseDown(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
