﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <ControlTemplate x:Key="ConnectorThumbTemplate"
                     TargetType="Control">
        <Ellipse Width="14"
                 Height="14"
                 Stroke="{TemplateBinding BorderBrush}"
                 Fill="{TemplateBinding Background}"
                 StrokeThickness="2" />
    </ControlTemplate>

    <LinearGradientBrush x:Key="FadeOpacityMask"
                         StartPoint="0 0"
                         EndPoint="1 0">
        <GradientStop Color="#22FFFFFF"
                      Offset="0" />
        <GradientStop Color="#88FFFFFF"
                      Offset="0.3" />
        <GradientStop Color="#88FFFFFF"
                      Offset="0.7" />
        <GradientStop Color="#22FFFFFF"
                      Offset="1" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="FadeOpacityMaskVertical"
                         StartPoint="0 0"
                         EndPoint="0 1">
        <GradientStop Color="#22FFFFFF"
                      Offset="0" />
        <GradientStop Color="#88FFFFFF"
                      Offset="0.3" />
        <GradientStop Color="#88FFFFFF"
                      Offset="0.7" />
        <GradientStop Color="#22FFFFFF"
                      Offset="1" />
    </LinearGradientBrush>

    <Style TargetType="{x:Type local:NodeOutput}">
        <Setter Property="BorderBrush"
                Value="DodgerBlue" />
        <Setter Property="Background"
                Value="#2D2D30" />
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="Padding"
                Value="4 2" />
        <Setter Property="ConnectorTemplate"
                Value="{StaticResource ConnectorThumbTemplate}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:NodeOutput}">
                    <Grid Background="{TemplateBinding Background}">
                        <Border Visibility="Collapsed"
                                x:Name="Highlight"
                                OpacityMask="{StaticResource FadeOpacityMask}"
                                Background="{TemplateBinding BorderBrush}" />

                        <StackPanel Orientation="{TemplateBinding Orientation}"
                                    HorizontalAlignment="Right"
                                    Margin="{TemplateBinding Padding}">

                            <ContentPresenter ContentSource="Header" />

                            <Control x:Name="PART_Connector"
                                     Focusable="False"
                                     Margin="5 0 0 0"
                                     VerticalAlignment="Center"
                                     HorizontalAlignment="Center"
                                     Background="Transparent"
                                     BorderBrush="{TemplateBinding BorderBrush}"
                                     Template="{TemplateBinding ConnectorTemplate}" />
                        </StackPanel>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Orientation"
                                 Value="Vertical">
                            <Setter TargetName="PART_Connector"
                                    Property="Margin"
                                    Value="0 5 0 0" />
                            <Setter Property="Padding"
                                    Value="2 4" />
                            <Setter TargetName="Highlight"
                                    Property="OpacityMask"
                                    Value="{StaticResource FadeOpacityMaskVertical}" />
                        </Trigger>

                        <Trigger Property="IsConnected"
                                 Value="True">
                            <Setter TargetName="PART_Connector"
                                    Property="Background"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                        <Trigger Property="local:PendingConnection.IsOverElement"
                                 Value="True">
                            <Setter TargetName="PART_Connector"
                                    Property="Background"
                                    Value="{Binding BorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter TargetName="Highlight"
                                    Property="Visibility"
                                    Value="Visible" />
                        </Trigger>
                        <Trigger Property="local:PendingConnection.IsOverElement"
                                 Value="True">
                            <Setter TargetName="Highlight"
                                    Property="Visibility"
                                    Value="Visible" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>