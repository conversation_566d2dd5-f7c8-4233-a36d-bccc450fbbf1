# SelectionType Enum  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**References:** [ConnectionContainer](Nodify_ConnectionContainer), [ItemContainer](Nodify_ItemContainer), [NodifyEditor](Nodify_NodifyEditor)  
  
Available selection logic.  
  
```csharp  
public enum SelectionType  
```  
  
## Fields  
  
### Append  
  
Adds items to the current selection.  
  
```csharp  
Append = 2;  
```  
  
### Invert  
  
Inverts the selection.  
  
```csharp  
Invert = 3;  
```  
  
### Remove  
  
Removes items from existing selection.  
  
```csharp  
Remove = 1;  
```  
  
### Replace  
  
Replaces the old selection.  
  
```csharp  
Replace = 0;  
```  
  
