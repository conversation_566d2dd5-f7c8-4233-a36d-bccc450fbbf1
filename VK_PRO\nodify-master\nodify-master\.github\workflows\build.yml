name: Build

on:
  push:
    branches: ["master", "release-v*"]
    paths-ignore:
      - "docs/**"
  pull_request:
    paths-ignore:
      - "docs/**"

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"
      - name: Build
        run: dotnet build --configuration Release
