﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Controls.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--NODIFY EDITOR-->

    <Color x:Key="NodifyEditor.BackgroundColor">#332155</Color>
    <Color x:Key="NodifyEditor.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="NodifyEditor.SelectionRectangleColor">#FD5618</Color>
    <Color x:Key="NodifyEditor.PushedAreaColor">#9b44dd</Color>
    <Color x:Key="NodifyEditor.CuttingLineColor">Red</Color>

    <!--ITEM CONTAINER-->

    <Color x:Key="ItemContainer.BorderColor">#662D91</Color>
    <Color x:Key="ItemContainer.SelectedColor">#FD5618</Color>

    <!--NODE-->
    <Color x:Key="Node.BackgroundColor">#662D91</Color>
    <Color x:Key="Node.ContentColor">#662D91</Color>
    <Color x:Key="Node.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="Node.HeaderForegroundColor">#E8E1F3</Color>
    <Color x:Key="Node.HeaderColor">#451E63</Color>
    <Color x:Key="Node.FooterColor">#451E63</Color>
    <Color x:Key="Node.BorderColor">Transparent</Color>

    <!--STATE NODE-->
    <Color x:Key="StateNode.BackgroundColor">#451E63</Color>
    <Color x:Key="StateNode.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="StateNode.BorderColor">#662D91</Color>
    <Color x:Key="StateNode.HighlightColor">#D6D3D6</Color>

    <!--GROUPING NODE-->
    <Color x:Key="GroupingNode.BackgroundColor">#2C1D4A</Color>
    <Color x:Key="GroupingNode.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="GroupingNode.HeaderColor">#451E63</Color>
    <Color x:Key="GroupingNode.BorderColor">Transparent</Color>

    <!--KNOT NODE-->
    <Color x:Key="KnotNode.BackgroundColor">Transparent</Color>
    <Color x:Key="KnotNode.ForegroundColor">#662D91</Color>
    <Color x:Key="KnotNode.BorderColor">Transparent</Color>

    <!--CONNECTOR-->
    <Color x:Key="Connector.BackgroundColor">Transparent</Color>
    <Color x:Key="Connector.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="Connector.BorderColor">#FD5618</Color>

    <!--NODE INPUT-->
    <Color x:Key="NodeInput.BackgroundColor">#662D91</Color>
    <Color x:Key="NodeInput.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="NodeInput.BorderColor">#FD5618</Color>

    <!--NODE OUTPUT-->
    <Color x:Key="NodeOutput.BackgroundColor">#662D91</Color>
    <Color x:Key="NodeOutput.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="NodeOutput.BorderColor">#FD5618</Color>

    <!--CONNECTION-->
    <Color x:Key="Connection.StrokeColor">#FD5618</Color>

    <!--LINE CONNECTION-->
    <Color x:Key="LineConnection.StrokeColor">#FD5618</Color>

    <!--CIRCUIT CONNECTION-->
    <Color x:Key="CircuitConnection.StrokeColor">#FD5618</Color>

    <!--STEP CONNECTION-->
    <Color x:Key="StepConnection.StrokeColor">#FD5618</Color>

    <!--PENDING CONNECTION-->
    <Color x:Key="PendingConnection.StrokeColor">#FD5618</Color>
    <Color x:Key="PendingConnection.BorderColor">#451E63</Color>
    <Color x:Key="PendingConnection.ForegroundColor">#E8E1F3</Color>
    <Color x:Key="PendingConnection.BackgroundColor">#2A1B47</Color>

    <!--MINIMAP-->
    <Color x:Key="Minimap.BackgroundColor">#2A1B47</Color>
    <Color x:Key="Minimap.ViewportStrokeColor">#9b44dd</Color>
    <Color x:Key="Minimap.ViewportBackgroundColor">#9b44dd</Color>
    <Color x:Key="MinimapItem.BackgroundColor">#FD5618</Color>

    <!--HOT KEYS-->
    <Color x:Key="HotKey.BackgroundColor">White</Color>
    <Color x:Key="HotKey.ForegroundColor">Black</Color>
    <Color x:Key="HotKey.BorderColor">#FD5618</Color>

</ResourceDictionary>