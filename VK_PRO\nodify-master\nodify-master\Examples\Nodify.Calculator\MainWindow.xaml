﻿<Window x:Class="Nodify.Calculator.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Nodify.Calculator"
        xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
        Background="{DynamicResource NodifyEditor.BackgroundBrush}"
        Foreground="{DynamicResource ForegroundBrush}"
        mc:Ignorable="d"
        Title="MainWindow"
        Height="650"
        Width="1200">
    <Window.DataContext>
        <local:ApplicationViewModel />
    </Window.DataContext>
    
    <Window.InputBindings>
        <KeyBinding Key="T"
                    Modifiers="Ctrl"
                    Command="{Binding Source={x:Static shared:ThemeManager.SetNextThemeCommand}}" />
        <KeyBinding Key="N"
                    Modifiers="Ctrl"
                    Command="{Binding AddEditorCommand}" />
        <KeyBinding Key="W"
                    Modifiers="Ctrl"
                    Command="{Binding CloseEditorCommand}"
                    CommandParameter="{Binding SelectedEditor.Id}"/>
    </Window.InputBindings>
    
    <Window.Resources>
        <shared:BindingProxy x:Key="Proxy" 
                             DataContext="{Binding}"/>
        
        <DataTemplate DataType="{x:Type local:EditorViewModel}">
            <local:EditorView/>
        </DataTemplate>
    </Window.Resources>
    
    <Grid>
        <shared:TabControlEx ItemsSource="{Binding Editors}"
                             SelectedItem="{Binding SelectedEditor}" 
                             AddTabCommand="{Binding AddEditorCommand}"
                             AutoScrollToEnd="{Binding AutoSelectNewEditor}">
            <shared:TabControlEx.ItemContainerStyle>
                <Style TargetType="{x:Type shared:TabItemEx}" 
                       BasedOn="{StaticResource {x:Type shared:TabItemEx}}">
                    <Setter Property="Header" 
                            Value="{Binding Name}"/>
                    <Setter Property="CloseTabCommand" 
                            Value="{Binding DataContext.CloseEditorCommand ,Source={StaticResource Proxy}}"/>
                    <Setter Property="CloseTabCommandParameter" 
                            Value="{Binding Id}"/>
                    <Setter Property="ToolTip"
                            Value="Double click to edit" />
                </Style>
            </shared:TabControlEx.ItemContainerStyle>
        </shared:TabControlEx>

        <Expander Header="Click to hide/show"
                  IsExpanded="True"
                  Margin="10"
                  HorizontalAlignment="Left"
                  VerticalAlignment="Bottom">
            <Border MaxWidth="325"
                    MaxHeight="300"
                    CornerRadius="3">
                <Border.Background>
                    <SolidColorBrush Color="{DynamicResource BackgroundColor}"
                                     Opacity="0.7" />
                </Border.Background>
                <ScrollViewer HorizontalScrollBarVisibility="Disabled">
                    <StackPanel Margin="10"
                                IsHitTestVisible="False">
                        <StackPanel.Resources>
                            <Style TargetType="{x:Type TextBlock}"
                                   BasedOn="{StaticResource {x:Type TextBlock}}">
                                <Setter Property="Margin"
                                        Value="0 0 0 5" />
                            </Style>
                        </StackPanel.Resources>

                        <StackPanel Margin="0 0 0 20">
                            <TextBlock Text="(New) Drag and drop nodes from the toolbox"
                                   TextWrapping="Wrap"
                                   Foreground="{DynamicResource NodeInput.BorderBrush}"
                                   FontWeight="Bold"/>
                        </StackPanel>
                        <TextBlock TextWrapping="Wrap">
                            <Run Foreground="Red"
                                    FontWeight="Bold">CTRL + N/W</Run>
                            <Run>: open/close editor</Run>
                        </TextBlock>
                        <TextBlock TextWrapping="Wrap">
                            <Run Foreground="Red"
                                    FontWeight="Bold">ALT + Click</Run>
                            <Run>: disconnect connector</Run>
                        </TextBlock>
                        <TextBlock TextWrapping="Wrap">
                            <Run Foreground="Red"
                                    FontWeight="Bold">Right Click</Run>
                            <Run>: show operations menu (create nodes)</Run>
                        </TextBlock>
                        <TextBlock TextWrapping="Wrap">
                            <Run Foreground="Red"
                                    FontWeight="Bold">Delete</Run>
                            <Run>:  delete selection</Run>
                        </TextBlock>
                        <TextBlock TextWrapping="Wrap">
                            <Run Foreground="Red"
                                    FontWeight="Bold">CTRL + T</Run>
                            <Run>: change theme</Run>
                        </TextBlock>
                        <TextBlock TextWrapping="Wrap">
                            <Run Foreground="Red"
                                    FontWeight="Bold">C</Run>
                            <Run>: group selection (hold SHIFT and mouse drag the header to move the group node alone)</Run>
                        </TextBlock>
                        <TextBlock Text="Drag a connection and drop it on the editor"
                                TextWrapping="Wrap"
                                FontWeight="Bold" />
                        <TextBlock Text="Hover over a connector to see its value"
                                   TextWrapping="Wrap"
                                   FontWeight="Bold" />
                        <TextBlock Text="Create a Calculator node and double click it to open"
                                   TextWrapping="Wrap"
                                   FontWeight="Bold" />
                        <TextBlock Text="Create an Operation Graph and add operations to it"
                                   TextWrapping="Wrap"
                                   FontWeight="Bold" />
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Expander>
    </Grid>
</Window>
