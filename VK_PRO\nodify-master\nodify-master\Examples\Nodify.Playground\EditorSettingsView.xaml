﻿<UserControl x:Class="Nodify.Playground.EditorSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Nodify.Playground"
             xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
             d:Foreground="{DynamicResource ForegroundBrush}"
             d:Background="{DynamicResource PanelBackgroundBrush}"
             mc:Ignorable="d">

    <StackPanel>
        <Border BorderThickness="1"
                Padding="10"
                HorizontalAlignment="Stretch">
            <local:SettingsView Items="{Binding Source={x:Static local:EditorSettings.Instance}, Path=Settings}"/>
        </Border>
        <Expander
            Header="Advanced"
            Padding="0 5 0 0"
            BorderThickness="0 0 0 1"
            IsExpanded="True"
            BorderBrush="{DynamicResource BackgroundBrush}">
            <Expander.Style>
                <Style TargetType="{x:Type Expander}"
                       BasedOn="{StaticResource {x:Type Expander}}">
                    <Setter Property="Tag"
                            Value="{StaticResource ExpandRightIcon}" />
                    <Style.Triggers>
                        <Trigger Property="IsExpanded"
                                 Value="True">
                            <Setter Property="Tag"
                                    Value="{StaticResource ExpandDownIcon}" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Expander.Style>

            <Border BorderThickness="1"
                    Padding="10"
                    HorizontalAlignment="Stretch">
                <local:SettingsView Items="{Binding Source={x:Static local:EditorSettings.Instance}, Path=AdvancedSettings}"/>
            </Border>
        </Expander>
    </StackPanel>

</UserControl>