﻿<Application x:Class="Nodify.StateMachine.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Icons.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.StateMachine;component/Themes/Light.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
