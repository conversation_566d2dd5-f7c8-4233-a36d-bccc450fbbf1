# ConnectionState.Disconnect Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<BaseConnection\>](Nodify_Interactivity_InputElementState_TElement_) → [ConnectionState.Disconnect](Nodify_Interactivity_ConnectionState_Disconnect)  
  
**References:** [BaseConnection](Nodify_BaseConnection)  
  
```csharp  
public class Disconnect : InputElementState<BaseConnection>  
```  
  
## Constructors  
  
### ConnectionState.Disconnect(BaseConnection)  
  
```csharp  
public Disconnect(BaseConnection connection);  
```  
  
**Parameters**  
  
`connection` [BaseConnection](Nodify_BaseConnection)  
  
## Methods  
  
### OnMouseDown(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseDown(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
### OnMouseUp(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseUp(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
