# Alignment Enum  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**References:** [EditorCommands](Nodify_EditorCommands), [NodifyEditor](Nodify_NodifyEditor)  
  
Specifies the possible alignment values used by the NodifyEditor.AlignSelection(Alignment) method.  
  
```csharp  
public enum Alignment  
```  
  
## Fields  
  
### Bottom  
  
```csharp  
Bottom = 2;  
```  
  
### Center  
  
```csharp  
Center = 5;  
```  
  
### Left  
  
```csharp  
Left = 1;  
```  
  
### Middle  
  
```csharp  
Middle = 4;  
```  
  
### Right  
  
```csharp  
Right = 3;  
```  
  
### Top  
  
```csharp  
Top = 0;  
```  
  
