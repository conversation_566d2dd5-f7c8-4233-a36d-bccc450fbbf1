﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    <local:UnscaleTransformConverter x:Key="UnscaleTransformConverter" />
    <local:ScaleDoubleConverter x:Key="ScaleDoubleConverter" />
    <local:ScalePointConverter x:Key="ScalePointConverter" />

    <DataTemplate x:Key="ConnectionTemplate">
        <local:Connection Style="{DynamicResource {x:Type local:Connection}}" />
    </DataTemplate>

    <DataTemplate x:Key="PendingConnectionTemplate">
        <local:PendingConnection IsTabStop="False"
                                 IsHitTestVisible="False" />
    </DataTemplate>

    <Style x:Key="SelectionRectangleStyle"
           TargetType="Rectangle">
        <Setter Property="Stroke"
                Value="DodgerBlue" />
        <Setter Property="StrokeThickness"
                Value="1" />
        <Setter Property="Fill">
            <Setter.Value>
                <SolidColorBrush Opacity="0.1"
                                 Color="DodgerBlue" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PushedAreaStyle"
           TargetType="Rectangle">
        <Setter Property="Stroke"
                Value="#74747c" />
        <Setter Property="StrokeThickness"
                Value="1" />
        <Setter Property="Fill">
            <Setter.Value>
                <SolidColorBrush Opacity="0.2"
                                 Color="#74747c" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:NodifyEditor}">
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="Background"
                Value="#1E1E1E" />
        <Setter Property="EnableRealtimeSelection"
                Value="True" />
        <Setter Property="DisplayConnectionsOnTop"
                Value="False" />
        <Setter Property="SelectionRectangleStyle"
                Value="{StaticResource SelectionRectangleStyle}" />
        <Setter Property="PushedAreaStyle"
                Value="{StaticResource PushedAreaStyle}" />
        <Setter Property="ConnectionTemplate"
                Value="{StaticResource ConnectionTemplate}" />
        <Setter Property="PendingConnectionTemplate"
                Value="{StaticResource PendingConnectionTemplate}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:NodifyEditor}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            ClipToBounds="True">
                        <Canvas RenderTransform="{TemplateBinding ViewportTransform}">
                            <local:NodifyCanvas x:Name="PART_ItemsHost"
                                                IsItemsHost="True"
                                                Extent="{Binding ItemsExtent, Mode=OneWayToSource, RelativeSource={RelativeSource TemplatedParent}}" />

                            <local:ConnectionsMultiSelector  x:Name="PART_ConnectionsHost"
                                                             ItemsSource="{TemplateBinding Connections}"
                                                             SelectedItem="{Binding SelectedConnection, RelativeSource={RelativeSource TemplatedParent}}"
                                                             SelectedItems="{TemplateBinding SelectedConnections}"
                                                             CanSelectMultipleItems="{TemplateBinding CanSelectMultipleConnections}"
                                                             ItemTemplate="{TemplateBinding ConnectionTemplate}"
                                                             IsTabStop="False">
                                <local:ConnectionsMultiSelector.Style>
                                    <Style TargetType="local:ConnectionsMultiSelector">
                                        <Setter Property="Panel.ZIndex"
                                                Value="-1" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding DisplayConnectionsOnTop, RelativeSource={RelativeSource TemplatedParent}}"
                                                         Value="True">
                                                <Setter Property="Panel.ZIndex"
                                                        Value="0" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </local:ConnectionsMultiSelector.Style>
                                <local:ConnectionsMultiSelector.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <Canvas />
                                    </ItemsPanelTemplate>
                                </local:ConnectionsMultiSelector.ItemsPanel>
                            </local:ConnectionsMultiSelector>

                            <ContentPresenter ContentSource="PendingConnection" />

                            <Rectangle Style="{TemplateBinding SelectionRectangleStyle}"
                                       RenderTransform="{Binding ViewportTransform, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource UnscaleTransformConverter}}"
                                       Canvas.Top="{Binding SelectedArea.Y, RelativeSource={RelativeSource TemplatedParent}}"
                                       Canvas.Left="{Binding SelectedArea.X, RelativeSource={RelativeSource TemplatedParent}}"
                                       Visibility="{TemplateBinding IsSelecting, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Rectangle.Width>
                                    <MultiBinding Converter="{StaticResource ScaleDoubleConverter}">
                                        <Binding Path="SelectedArea.Width"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ViewportZoom"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </Rectangle.Width>
                                <Rectangle.Height>
                                    <MultiBinding Converter="{StaticResource ScaleDoubleConverter}">
                                        <Binding Path="SelectedArea.Height"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ViewportZoom"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </Rectangle.Height>
                            </Rectangle>

                            <Rectangle Style="{TemplateBinding PushedAreaStyle}"
                                       RenderTransform="{Binding ViewportTransform, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource UnscaleTransformConverter}}"
                                       Canvas.Top="{Binding PushedArea.Y, RelativeSource={RelativeSource TemplatedParent}}"
                                       Canvas.Left="{Binding PushedArea.X, RelativeSource={RelativeSource TemplatedParent}}"
                                       Visibility="{TemplateBinding IsPushingItems, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Rectangle.Width>
                                    <MultiBinding Converter="{StaticResource ScaleDoubleConverter}">
                                        <Binding Path="PushedArea.Width"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ViewportZoom"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </Rectangle.Width>
                                <Rectangle.Height>
                                    <MultiBinding Converter="{StaticResource ScaleDoubleConverter}">
                                        <Binding Path="PushedArea.Height"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ViewportZoom"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </Rectangle.Height>
                            </Rectangle>

                            <local:CuttingLine Style="{TemplateBinding CuttingLineStyle}"
                                               Visibility="{TemplateBinding IsCutting, Converter={StaticResource BooleanToVisibilityConverter}}"
                                               RenderTransform="{Binding ViewportTransform, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource UnscaleTransformConverter}}">
                                <local:CuttingLine.StartPoint>
                                    <MultiBinding Converter="{StaticResource ScalePointConverter}">
                                        <Binding Path="CuttingLineStart"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ViewportZoom"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </local:CuttingLine.StartPoint>
                                <local:CuttingLine.EndPoint>
                                    <MultiBinding Converter="{StaticResource ScalePointConverter}">
                                        <Binding Path="CuttingLineEnd"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="ViewportZoom"
                                                 RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </local:CuttingLine.EndPoint>
                            </local:CuttingLine>

                            <local:DecoratorsControl ItemsSource="{TemplateBinding Decorators}"
                                                     ItemContainerStyle="{TemplateBinding DecoratorContainerStyle}"
                                                     ItemTemplate="{TemplateBinding DecoratorTemplate}"
                                                     IsTabStop="False">
                                <local:DecoratorsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <local:NodifyCanvas Extent="{Binding DecoratorsExtent, Mode=OneWayToSource, RelativeSource={RelativeSource AncestorType=local:NodifyEditor}}" />
                                    </ItemsPanelTemplate>
                                </local:DecoratorsControl.ItemsPanel>
                            </local:DecoratorsControl>
                        </Canvas>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsPushingItems"
                               Value="True" />
                    <Condition Property="PushedAreaOrientation"
                               Value="Horizontal" />
                </MultiTrigger.Conditions>
                <Setter Property="Cursor"
                        Value="SizeWE" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsPushingItems"
                               Value="True" />
                    <Condition Property="PushedAreaOrientation"
                               Value="Vertical" />
                </MultiTrigger.Conditions>
                <Setter Property="Cursor"
                        Value="SizeNS" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>