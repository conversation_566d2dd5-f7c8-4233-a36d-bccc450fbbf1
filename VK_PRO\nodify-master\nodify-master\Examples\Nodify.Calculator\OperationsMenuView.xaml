﻿<UserControl x:Class="Nodify.Calculator.OperationsMenuView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Nodify.Calculator"
             xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
             mc:Ignorable="d"
             MinWidth="250"
             d:DesignHeight="400"
             d:DesignWidth="250"
             d:DataContext="{d:DesignInstance local:OperationsMenuViewModel}"
             Visibility="{Binding IsVisible, Converter={shared:BooleanToVisibilityConverter}}">
    <UserControl.Resources>
        <Style TargetType="{x:Type TextBlock}"
               BasedOn="{StaticResource {x:Type TextBlock}}">
            <Setter Property="Foreground"
                    Value="{DynamicResource ForegroundBrush}" />
        </Style>
    </UserControl.Resources>

    <Border Padding="7"
            CornerRadius="3"
            Background="{DynamicResource Node.BackgroundBrush}"
            BorderBrush="{StaticResource NodifyEditor.SelectionRectangleStrokeBrush}"
            BorderThickness="2">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <ItemsControl Grid.Row="1"
                          x:Name="OperationsList"
                          Focusable="True"
                          KeyboardNavigation.TabNavigation="Cycle"
                          ItemsSource="{Binding AvailableOperations}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate DataType="{x:Type local:OperationInfoViewModel}">
                        <Button Content="{Binding Title}"
                                Command="{Binding DataContext.CreateOperationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                CommandParameter="{Binding}"
                                ClickMode="Press"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                Foreground="{DynamicResource ForegroundBrush}"
                                Padding="3"
                                Cursor="Hand"
                                HorizontalContentAlignment="Left">
                            <Button.Style>
                                <Style TargetType="{x:Type Button}">
                                    <Setter Property="FocusVisualStyle"
                                            Value="{StaticResource {x:Static SystemParameters.FocusVisualStyleKey}}" />
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="{x:Type Button}">
                                                <Border Name="Border"
                                                        Background="{TemplateBinding Background}"
                                                        Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter />
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver"
                                                             Value="True">
                                                        <Setter Property="Background"
                                                                TargetName="Border"
                                                                Value="{DynamicResource NodeInput.BorderBrush}" />
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Grid>
    </Border>
</UserControl>
