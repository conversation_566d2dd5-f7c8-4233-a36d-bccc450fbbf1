﻿<UserControl x:Class="Nodify.Playground.NodifyEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Nodify.Playground"
             xmlns:nodify="https://miroiu.github.io/nodify"
             xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
             mc:Ignorable="d"
             Background="{DynamicResource NodifyEditor.BackgroundBrush}"
             d:DesignHeight="450"
             d:DesignWidth="800">

    <UserControl.DataContext>
        <local:NodifyEditorViewModel />
    </UserControl.DataContext>

    <UserControl.Resources>
        <shared:RandomBrushConverter x:Key="RandomBrushConverter" />
        <local:FlowToDirectionConverter x:Key="FlowToDirectionConverter" />
        <local:FlowToConnectorPositionConverter x:Key="FlowToConnectorPositionConverter" />

        <GeometryDrawing x:Key="SmallGridGeometry"
                         Geometry="M0,0 L0,1 0.03,1 0.03,0.03 1,0.03 1,0 Z"
                         Brush="{DynamicResource GridLinesBrush}" />

        <GeometryDrawing x:Key="LargeGridGeometry"
                         Geometry="M0,0 L0,1 0.015,1 0.015,0.015 1,0.015 1,0 Z"
                         Brush="{DynamicResource GridLinesBrush}" />

        <DrawingBrush x:Key="SmallGridLinesDrawingBrush"
                      TileMode="Tile"
                      ViewportUnits="Absolute"
                      Viewport="{Binding GridSpacing, Source={x:Static local:EditorSettings.Instance}, Converter={local:UIntToRectConverter}}"
                      Transform="{Binding ViewportTransform, ElementName=Editor}"
                      Drawing="{StaticResource SmallGridGeometry}" />

        <DrawingBrush x:Key="LargeGridLinesDrawingBrush"
                      TileMode="Tile"
                      ViewportUnits="Absolute"
                      Opacity="0.5"
                      Viewport="{Binding GridSpacing, Source={x:Static local:EditorSettings.Instance}, Converter={local:UIntToRectConverter  Multiplier=10}}"
                      Transform="{Binding ViewportTransform, ElementName=Editor}"
                      Drawing="{StaticResource LargeGridGeometry}" />

        <SolidColorBrush x:Key="SquareConnectorColor"
                         Color="MediumSlateBlue" />
        <SolidColorBrush x:Key="TriangleConnectorColor"
                         Color="MediumVioletRed" />
        <SolidColorBrush x:Key="SquareConnectorOutline"
                         Color="MediumSlateBlue"
                         Opacity="0.15" />
        <SolidColorBrush x:Key="TriangleConnectorOutline"
                         Color="MediumVioletRed"
                         Opacity="0.15" />

        <UIElement x:Key="ConnectionAnimationPlaceholder"
                   Opacity="1" />

        <Storyboard x:Key="HighlightConnection">
            <DoubleAnimation Storyboard.Target="{StaticResource ConnectionAnimationPlaceholder}"
                             Storyboard.TargetProperty="(UIElement.Opacity)"
                             Duration="0:0:0.3"
                             From="1"
                             To="0.3" />
        </Storyboard>

        <Style x:Key="ConnectionStyle"
               TargetType="{x:Type nodify:BaseConnection}"
               BasedOn="{StaticResource {x:Type nodify:BaseConnection}}">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Input.Shape}"
                             Value="{x:Static local:ConnectorShape.Square}">
                    <Setter Property="Stroke"
                            Value="{StaticResource SquareConnectorColor}" />
                    <Setter Property="Fill"
                            Value="{StaticResource SquareConnectorColor}" />
                    <Setter Property="OutlineBrush"
                            Value="{StaticResource SquareConnectorOutline}" />
                </DataTrigger>
                <DataTrigger Binding="{Binding Input.Shape}"
                             Value="{x:Static local:ConnectorShape.Triangle}">
                    <Setter Property="Stroke"
                            Value="{StaticResource TriangleConnectorColor}" />
                    <Setter Property="Fill"
                            Value="{StaticResource TriangleConnectorColor}" />
                    <Setter Property="OutlineBrush"
                            Value="{StaticResource TriangleConnectorOutline}" />
                </DataTrigger>
                <Trigger Property="IsMouseDirectlyOver"
                         Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Name="HighlightConnection"
                                         Storyboard="{StaticResource HighlightConnection}" />
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <RemoveStoryboard BeginStoryboardName="HighlightConnection" />
                    </Trigger.ExitActions>
                    <Setter Property="Opacity"
                            Value="1" />
                </Trigger>
                <Trigger Property="IsSelectable"
                         Value="True">
                    <Setter Property="Cursor"
                            Value="Hand" />
                </Trigger>
                <MultiTrigger>
                    <MultiTrigger.Conditions>
                        <Condition Property="IsMouseDirectlyOver"
                                   Value="False" />
                        <Condition Property="IsSelected"
                                   Value="False" />
                    </MultiTrigger.Conditions>
                    <MultiTrigger.Setters>
                        <Setter Property="OutlineBrush"
                                Value="Transparent" />
                    </MultiTrigger.Setters>
                </MultiTrigger>
            </Style.Triggers>
            <Setter Property="Opacity"
                    Value="{Binding Source={StaticResource ConnectionAnimationPlaceholder}, Path=Opacity}" />
            <Setter Property="Stroke"
                    Value="{DynamicResource Connection.StrokeBrush}" />
            <Setter Property="Fill"
                    Value="{DynamicResource Connection.StrokeBrush}" />
            <Setter Property="OutlineBrush">
                <Setter.Value>
                    <SolidColorBrush Color="{DynamicResource Connection.StrokeColor}"
                                     Opacity="0.15" />
                </Setter.Value>
            </Setter>
            <Setter Property="ToolTip"
                    Value="Double click to split" />
            <Setter Property="Source"
                    Value="{Binding Output.Anchor}" />
            <Setter Property="Target"
                    Value="{Binding Input.Anchor}" />
            <Setter Property="SplitCommand"
                    Value="{Binding SplitCommand}" />
            <Setter Property="DisconnectCommand"
                    Value="{Binding DisconnectCommand}" />
            <Setter Property="SourceOffsetMode"
                    Value="{Binding ConnectionSourceOffsetMode, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="TargetOffsetMode"
                    Value="{Binding ConnectionTargetOffsetMode, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="SourceOffset"
                    Value="{Binding ConnectionSourceOffset.Size, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="TargetOffset"
                    Value="{Binding ConnectionTargetOffset.Size, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="ArrowSize"
                    Value="{Binding ConnectionArrowSize.Size, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="ArrowEnds"
                    Value="{Binding ArrowHeadEnds, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="ArrowShape"
                    Value="{Binding ArrowHeadShape, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="Spacing"
                    Value="{Binding ConnectionSpacing, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="Direction"
                    Value="{Binding Output.Flow, Converter={StaticResource FlowToDirectionConverter}}" />
            <Setter Property="SourceOrientation"
                    Value="{Binding Output.Node.Orientation}" />
            <Setter Property="TargetOrientation"
                    Value="{Binding Input.Node.Orientation}" />
            <Setter Property="DirectionalArrowsCount"
                    Value="{Binding DirectionalArrowsCount, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="DirectionalArrowsOffset"
                    Value="{Binding DirectionalArrowsOffset, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="IsAnimatingDirectionalArrows"
                    Value="{Binding IsAnimatingConnections, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="DirectionalArrowsAnimationDuration"
                    Value="{Binding DirectionalArrowsAnimationDuration, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="Text"
                    Value="{Binding ConnectionText, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="IsSelectable"
                    Value="{Binding SelectableConnections, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="IsSelected"
                    Value="{Binding IsSelected}" />
            <Setter Property="StrokeThickness"
                    Value="{Binding ConnectionStrokeThickness, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="OutlineThickness"
                    Value="{Binding ConnectionOutlineThickness, Source={x:Static local:EditorSettings.Instance}}" />
            <Setter Property="FocusVisualPadding"
                    Value="{Binding ConnectionFocusVisualPadding, Source={x:Static local:EditorSettings.Instance}}" />
        </Style>

        <DataTemplate x:Key="CircuitConnectionTemplate">
            <nodify:CircuitConnection Style="{StaticResource ConnectionStyle}"
                                      Angle="{Binding CircuitConnectionAngle, Source={x:Static local:EditorSettings.Instance}}"
                                      CornerRadius="{Binding ConnectionCornerRadius, Source={x:Static local:EditorSettings.Instance}}" />
        </DataTemplate>

        <DataTemplate x:Key="StepConnectionTemplate">
            <nodify:StepConnection Style="{StaticResource ConnectionStyle}"
                                   CornerRadius="{Binding ConnectionCornerRadius, Source={x:Static local:EditorSettings.Instance}}"
                                   SourcePosition="{Binding ., Converter={StaticResource FlowToConnectorPositionConverter}, ConverterParameter=Output}"
                                   TargetPosition="{Binding ., Converter={StaticResource FlowToConnectorPositionConverter}, ConverterParameter=Input}" />
        </DataTemplate>

        <DataTemplate x:Key="LineConnectionTemplate">
            <nodify:LineConnection Style="{StaticResource ConnectionStyle}"
                                   CornerRadius="{Binding ConnectionCornerRadius, Source={x:Static local:EditorSettings.Instance}}" />
        </DataTemplate>

        <DataTemplate x:Key="ConnectionTemplate">
            <nodify:Connection Style="{StaticResource ConnectionStyle}" />
        </DataTemplate>

        <ControlTemplate x:Key="SquareConnector"
                         TargetType="Control">
            <Rectangle Width="14"
                       Height="14"
                       StrokeDashCap="Round"
                       StrokeLineJoin="Round"
                       StrokeStartLineCap="Round"
                       StrokeEndLineCap="Round"
                       Stroke="{TemplateBinding BorderBrush}"
                       Fill="{TemplateBinding Background}"
                       StrokeThickness="2" />
        </ControlTemplate>

        <ControlTemplate x:Key="TriangleConnector"
                         TargetType="Control">
            <Polygon Width="14"
                     Height="14"
                     Points="1,13 13,13 7,1"
                     StrokeDashCap="Round"
                     StrokeLineJoin="Round"
                     StrokeStartLineCap="Round"
                     StrokeEndLineCap="Round"
                     Stroke="{TemplateBinding BorderBrush}"
                     Fill="{TemplateBinding Background}"
                     StrokeThickness="2" />
        </ControlTemplate>

        <Storyboard x:Key="MarchingAnts">
            <DoubleAnimation RepeatBehavior="Forever"
                             Storyboard.TargetProperty="StrokeDashOffset"
                             BeginTime="00:00:00"
                             Duration="0:3:0"
                             From="1000"
                             To="0" />
        </Storyboard>

        <Style x:Key="SelectionRectangleStyle"
               TargetType="Rectangle"
               BasedOn="{StaticResource NodifyEditor.SelectionRectangleStyle}">
            <Setter Property="StrokeDashArray"
                    Value="4 4" />
            <Setter Property="StrokeThickness"
                    Value="2" />
            <Style.Triggers>
                <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                    <BeginStoryboard Storyboard="{StaticResource MarchingAnts}" />
                </EventTrigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CuttingLineStyle"
               TargetType="{x:Type nodify:CuttingLine}"
               BasedOn="{StaticResource {x:Type nodify:CuttingLine}}">
            <Setter Property="StrokeDashArray"
                    Value="1 1" />
            <Setter Property="StrokeThickness"
                    Value="2" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <nodify:NodifyEditor x:Name="Editor"
                             ItemsSource="{Binding Nodes}"
                             SelectedItem="{Binding SelectedNode}"
                             SelectedItems="{Binding SelectedNodes}"
                             CanSelectMultipleItems="{Binding CanSelectMultipleNodes, Source={x:Static local:EditorSettings.Instance}}"
                             Connections="{Binding Connections}"
                             SelectedConnection="{Binding SelectedConnection}"
                             SelectedConnections="{Binding SelectedConnections}"
                             CanSelectMultipleConnections="{Binding CanSelectMultipleConnections, Source={x:Static local:EditorSettings.Instance}}"
                             PendingConnection="{Binding PendingConnection}"
                             DisconnectConnectorCommand="{Binding DisconnectConnectorCommand}"
                             ViewportLocation="{Binding Location.Value, Source={x:Static local:EditorSettings.Instance}}"
                             ViewportSize="{Binding ViewportSize, Mode=OneWayToSource}"
                             ViewportZoom="{Binding Zoom, Source={x:Static local:EditorSettings.Instance}}"
                             MinViewportZoom="{Binding MinZoom, Source={x:Static local:EditorSettings.Instance}}"
                             MaxViewportZoom="{Binding MaxZoom, Source={x:Static local:EditorSettings.Instance}}"
                             AutoPanSpeed="{Binding AutoPanningSpeed, Source={x:Static local:EditorSettings.Instance}}"
                             AutoPanEdgeDistance="{Binding AutoPanningEdgeDistance, Source={x:Static local:EditorSettings.Instance}}"
                             GridCellSize="{Binding GridSpacing, Source={x:Static local:EditorSettings.Instance}}"
                             EnableRealtimeSelection="{Binding EnableRealtimeSelection, Source={x:Static local:EditorSettings.Instance}}"
                             DisableAutoPanning="{Binding DisableAutoPanning, Source={x:Static local:EditorSettings.Instance}}"
                             DisablePanning="{Binding DisablePanning, Source={x:Static local:EditorSettings.Instance}}"
                             DisableZooming="{Binding DisableZooming, Source={x:Static local:EditorSettings.Instance}}"
                             DisplayConnectionsOnTop="{Binding DisplayConnectionsOnTop, Source={x:Static local:EditorSettings.Instance}}"
                             BringIntoViewSpeed="{Binding BringIntoViewSpeed, Source={x:Static local:EditorSettings.Instance}}"
                             BringIntoViewMaxDuration="{Binding BringIntoViewMaxDuration, Source={x:Static local:EditorSettings.Instance}}"
                             SelectionRectangleStyle="{StaticResource SelectionRectangleStyle}"
                             CuttingLineStyle="{StaticResource CuttingLineStyle}">
            <nodify:NodifyEditor.Style>
                <Style TargetType="{x:Type nodify:NodifyEditor}"
                       BasedOn="{StaticResource {x:Type nodify:NodifyEditor}}">
                    <Setter Property="ConnectionTemplate"
                            Value="{StaticResource ConnectionTemplate}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding ShowGridLines, Source={x:Static local:PlaygroundSettings.Instance}}"
                                     Value="True">
                            <Setter Property="Background"
                                    Value="{StaticResource SmallGridLinesDrawingBrush}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ConnectionStyle, Mode=TwoWay, Source={x:Static local:EditorSettings.Instance}}"
                                     Value="Line">
                            <Setter Property="ConnectionTemplate"
                                    Value="{StaticResource LineConnectionTemplate}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ConnectionStyle, Mode=TwoWay, Source={x:Static local:EditorSettings.Instance}}"
                                     Value="Circuit">
                            <Setter Property="ConnectionTemplate"
                                    Value="{StaticResource CircuitConnectionTemplate}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding ConnectionStyle, Mode=TwoWay, Source={x:Static local:EditorSettings.Instance}}"
                                     Value="Step">
                            <Setter Property="ConnectionTemplate"
                                    Value="{StaticResource StepConnectionTemplate}" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </nodify:NodifyEditor.Style>

            <nodify:NodifyEditor.InputBindings>
                <KeyBinding Key="Delete"
                            Command="{Binding DeleteSelectionCommand}" />
                <KeyBinding Key="C"
                            Command="{Binding CommentSelectionCommand}" />
            </nodify:NodifyEditor.InputBindings>

            <nodify:NodifyEditor.Resources>
                <Style TargetType="{x:Type nodify:PendingConnection}"
                       BasedOn="{StaticResource {x:Type nodify:PendingConnection}}">
                    <Setter Property="CompletedCommand"
                            Value="{Binding Graph.CreateConnectionCommand}" />
                    <Setter Property="Source"
                            Value="{Binding Source, Mode=OneWayToSource}" />
                    <Setter Property="Target"
                            Value="{Binding PreviewTarget, Mode=OneWayToSource}" />
                    <Setter Property="PreviewTarget"
                            Value="{Binding PreviewTarget, Mode=OneWayToSource}" />
                    <Setter Property="Content"
                            Value="{Binding PreviewText}" />
                    <Setter Property="EnablePreview"
                            Value="{Binding EnablePendingConnectionPreview, Source={x:Static local:EditorSettings.Instance}}" />
                    <Setter Property="EnableSnapping"
                            Value="{Binding EnablePendingConnectionSnapping, Source={x:Static local:EditorSettings.Instance}}" />
                    <Setter Property="AllowOnlyConnectors"
                            Value="{Binding AllowConnectingToConnectorsOnly, Source={x:Static local:EditorSettings.Instance}}" />
                    <Setter Property="Direction"
                            Value="{Binding Source.Flow, Converter={StaticResource FlowToDirectionConverter}}" />
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="{x:Type nodify:PendingConnection}">
                                <Canvas>
                                    <nodify:Connection Source="{TemplateBinding SourceAnchor}"
                                                       Target="{TemplateBinding TargetAnchor}"
                                                       Direction="{TemplateBinding Direction}"
                                                       SourceOrientation="{Binding Source.Node.Orientation}"
                                                       TargetOrientation="{Binding TargetOrientation}"
                                                       DirectionalArrowsCount="{Binding DirectionalArrowsCount, Source={x:Static local:EditorSettings.Instance}}"
                                                       StrokeThickness="{TemplateBinding StrokeThickness}"
                                                       SourceOffset="{Binding ConnectionSourceOffset.Size, Source={x:Static local:EditorSettings.Instance}}"
                                                       TargetOffset="{Binding ConnectionTargetOffset.Size, Source={x:Static local:EditorSettings.Instance}}"
                                                       SourceOffsetMode="{Binding ConnectionSourceOffsetMode, Source={x:Static local:EditorSettings.Instance}}"
                                                       TargetOffsetMode="None"
                                                       ArrowSize="{Binding ConnectionArrowSize.Size, Source={x:Static local:EditorSettings.Instance}}"
                                                       ArrowEnds="{Binding ArrowHeadEnds, Source={x:Static local:EditorSettings.Instance}}"
                                                       ArrowShape="{Binding ArrowHeadShape, Source={x:Static local:EditorSettings.Instance}}"
                                                       Spacing="{Binding ConnectionSpacing, Source={x:Static local:EditorSettings.Instance}}">
                                        <nodify:Connection.Style>
                                            <Style TargetType="nodify:Connection"
                                                   BasedOn="{StaticResource {x:Type nodify:Connection}}">
                                                <Setter Property="Stroke"
                                                        Value="{DynamicResource Connection.StrokeBrush}" />
                                                <Setter Property="Fill"
                                                        Value="{DynamicResource Connection.StrokeBrush}" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Source.Shape}"
                                                                 Value="{x:Static local:ConnectorShape.Square}">
                                                        <Setter Property="Stroke"
                                                                Value="{StaticResource SquareConnectorColor}" />
                                                        <Setter Property="Fill"
                                                                Value="{StaticResource SquareConnectorColor}" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Source.Shape}"
                                                                 Value="{x:Static local:ConnectorShape.Triangle}">
                                                        <Setter Property="Stroke"
                                                                Value="{StaticResource TriangleConnectorColor}" />
                                                        <Setter Property="Fill"
                                                                Value="{StaticResource TriangleConnectorColor}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </nodify:Connection.Style>
                                    </nodify:Connection>
                                    <Border Background="{TemplateBinding Background}"
                                            Canvas.Left="{Binding TargetAnchor.X, RelativeSource={RelativeSource TemplatedParent}}"
                                            Canvas.Top="{Binding TargetAnchor.Y, RelativeSource={RelativeSource TemplatedParent}}"
                                            Visibility="{Binding PreviewText, Converter={shared:StringToVisibilityConverter}}"
                                            Padding="{TemplateBinding Padding}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            CornerRadius="3"
                                            Margin="15">
                                        <ContentPresenter />
                                    </Border>
                                </Canvas>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>

                <Style TargetType="{x:Type nodify:Connector}"
                       BasedOn="{StaticResource {x:Type nodify:Connector}}">
                    <Setter Property="Anchor"
                            Value="{Binding Anchor, Mode=OneWayToSource}" />
                    <Setter Property="IsConnected"
                            Value="{Binding IsConnected}" />
                </Style>

                <Style TargetType="{x:Type nodify:NodeInput}"
                       BasedOn="{StaticResource {x:Type nodify:NodeInput}}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Shape}"
                                     Value="{x:Static local:ConnectorShape.Square}">
                            <Setter Property="ConnectorTemplate"
                                    Value="{StaticResource SquareConnector}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource SquareConnectorColor}" />
                            <Setter Property="HeaderTemplate">
                                <Setter.Value>
                                    <DataTemplate DataType="{x:Type local:ConnectorViewModel}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Title}"
                                                       Margin="0 0 5 0" />
                                            <TextBox Text="{Binding MaxConnections}"
                                                     MinWidth="30" />
                                        </StackPanel>
                                    </DataTemplate>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Shape}"
                                     Value="{x:Static local:ConnectorShape.Triangle}">
                            <Setter Property="ConnectorTemplate"
                                    Value="{StaticResource TriangleConnector}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource TriangleConnectorColor}" />
                            <Setter Property="HeaderTemplate">
                                <Setter.Value>
                                    <DataTemplate DataType="{x:Type local:ConnectorViewModel}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Title}"
                                                       Margin="0 0 5 0"
                                                       VerticalAlignment="Center" />
                                            <CheckBox />
                                        </StackPanel>
                                    </DataTemplate>
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                    </Style.Triggers>
                    <Setter Property="HeaderTemplate">
                        <Setter.Value>
                            <DataTemplate DataType="{x:Type local:ConnectorViewModel}">
                                <TextBlock Text="{Binding Title}" />
                            </DataTemplate>
                        </Setter.Value>
                    </Setter>
                    <Setter Property="Header"
                            Value="{Binding}" />
                    <Setter Property="Anchor"
                            Value="{Binding Anchor, Mode=OneWayToSource}" />
                    <Setter Property="IsConnected"
                            Value="{Binding IsConnected}" />
                    <Setter Property="Background"
                            Value="Transparent" />
                </Style>

                <Style TargetType="{x:Type nodify:NodeOutput}"
                       BasedOn="{StaticResource {x:Type nodify:NodeOutput}}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Shape}"
                                     Value="{x:Static local:ConnectorShape.Square}">
                            <Setter Property="ConnectorTemplate"
                                    Value="{StaticResource SquareConnector}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource SquareConnectorColor}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding Shape}"
                                     Value="{x:Static local:ConnectorShape.Triangle}">
                            <Setter Property="ConnectorTemplate"
                                    Value="{StaticResource TriangleConnector}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource TriangleConnectorColor}" />
                        </DataTrigger>
                    </Style.Triggers>
                    <Setter Property="Header"
                            Value="{Binding Title}" />
                    <Setter Property="Anchor"
                            Value="{Binding Anchor, Mode=OneWayToSource}" />
                    <Setter Property="IsConnected"
                            Value="{Binding IsConnected}" />
                    <Setter Property="Background"
                            Value="Transparent" />
                </Style>

                <DataTemplate DataType="{x:Type local:KnotNodeViewModel}">
                    <nodify:KnotNode Content="{Binding Connector}" />
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:CommentNodeViewModel}">
                    <nodify:GroupingNode ActualSize="{Binding Size}"
                                         Header="{Binding Title}"
                                         MovementMode="{Binding GroupingNodeMovement, Mode=TwoWay, Source={x:Static local:EditorSettings.Instance}}" />
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:FlowNodeViewModel}">
                    <nodify:Node Input="{Binding Input}"
                                 Output="{Binding Output}"
                                 Header="{Binding Title}" />
                </DataTemplate>

                <DataTemplate DataType="{x:Type local:VerticalNodeViewModel}">
                    <nodify:Node Header="{Binding Input}"
                                 Footer="{Binding Output}"
                                 Content="{Binding Title}">
                        <nodify:Node.ContentTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}"
                                           Margin="5" />
                            </DataTemplate>
                        </nodify:Node.ContentTemplate>
                        <nodify:Node.HeaderTemplate>
                            <DataTemplate>
                                <ItemsControl ItemsSource="{Binding}"
                                              Focusable="False">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate DataType="{x:Type local:ConnectorViewModel}">
                                            <nodify:NodeInput Orientation="Vertical" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel Orientation="Horizontal"
                                                        HorizontalAlignment="Center" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                </ItemsControl>
                            </DataTemplate>
                        </nodify:Node.HeaderTemplate>
                        <nodify:Node.FooterTemplate>
                            <DataTemplate>
                                <ItemsControl ItemsSource="{Binding}"
                                              Focusable="False">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate DataType="{x:Type local:ConnectorViewModel}">
                                            <nodify:NodeOutput Orientation="Vertical" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel Orientation="Horizontal"
                                                        HorizontalAlignment="Center" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                </ItemsControl>
                            </DataTemplate>
                        </nodify:Node.FooterTemplate>
                    </nodify:Node>
                </DataTemplate>
            </nodify:NodifyEditor.Resources>

            <nodify:NodifyEditor.ItemContainerStyle>
                <Style TargetType="{x:Type nodify:ItemContainer}"
                       BasedOn="{StaticResource {x:Type nodify:ItemContainer}}">
                    <Setter Property="BorderThickness"
                            Value="2" />
                    <Setter Property="SelectedBorderThickness"
                            Value="4" />
                    <Setter Property="IsSelectable"
                            Value="{Binding SelectableNodes, Source={x:Static local:EditorSettings.Instance}}" />
                    <Setter Property="IsDraggable"
                            Value="{Binding DraggableNodes, Source={x:Static local:EditorSettings.Instance}}" />
                    <Setter Property="CacheMode">
                        <Setter.Value>
                            <BitmapCache RenderAtScale="{Binding MaxZoom, Source={x:Static local:EditorSettings.Instance}}"
                                         EnableClearType="True" />
                        </Setter.Value>
                    </Setter>
                    <Setter Property="Location"
                            Value="{Binding Location}" />
                    <Style.Triggers>
                        <Trigger Property="IsSelected"
                                 Value="True">
                            <Setter Property="Panel.ZIndex"
                                    Value="1" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </nodify:NodifyEditor.ItemContainerStyle>
        </nodify:NodifyEditor>

        <Grid Background="{StaticResource LargeGridLinesDrawingBrush}"
              Visibility="{Binding ShowGridLines, Source={x:Static local:PlaygroundSettings.Instance}, Converter={shared:BooleanToVisibilityConverter}}"
              Panel.ZIndex="-2" />

        <nodify:Minimap ItemsSource="{Binding ItemsSource, ElementName=Editor}"
                        ViewportSize="{Binding ViewportSize, ElementName=Editor}"
                        ViewportLocation="{Binding ViewportLocation, ElementName=Editor}"
                        Visibility="{Binding ShowMinimap, Source={x:Static local:PlaygroundSettings.Instance}, Converter={shared:BooleanToVisibilityConverter}}"
                        IsReadOnly="{Binding DisableMinimapControls, Source={x:Static local:PlaygroundSettings.Instance}}"
                        ResizeToViewport="{Binding ResizeToViewport, Source={x:Static local:PlaygroundSettings.Instance}}"
                        MaxViewportOffset="{Binding MinimapMaxViewportOffset.Size, Source={x:Static local:PlaygroundSettings.Instance}}"
                        Zoom="Minimap_Zoom"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        Width="300"
                        Height="200"
                        Margin="5 40">
            <nodify:Minimap.ItemTemplate>
                <DataTemplate DataType="{x:Type local:NodeViewModel}">
                    <Grid />
                </DataTemplate>
            </nodify:Minimap.ItemTemplate>
            <nodify:Minimap.ItemContainerStyle>
                <Style TargetType="{x:Type nodify:MinimapItem}"
                       BasedOn="{StaticResource {x:Type nodify:MinimapItem}}">
                    <Setter Property="Location"
                            Value="{Binding Location}" />
                    <Setter Property="Width"
                            Value="150" />
                    <Setter Property="Height"
                            Value="130" />
                </Style>
            </nodify:Minimap.ItemContainerStyle>
        </nodify:Minimap>

        <StackPanel HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Margin="5 60"
                    Width="250">
            <Border CornerRadius="3"
                    Visibility="{Binding SelectedConnection, Converter={shared:BooleanToVisibilityConverter}}"
                    Background="{DynamicResource PanelBackgroundBrush}"
                    BorderThickness="1"
                    BorderBrush="{DynamicResource BorderBrush}"
                    Margin="0 0 0 10">
                <StackPanel Margin="10">
                    <StackPanel.Resources>
                        <Style TargetType="{x:Type TextBlock}"
                               BasedOn="{StaticResource {x:Type TextBlock}}">
                            <Setter Property="Margin"
                                    Value="0 0 0 5" />
                        </Style>
                    </StackPanel.Resources>

                    <StackPanel Margin="0 0 0 14">
                        <TextBlock Text="Selected connection"
                                   Foreground="{DynamicResource Node.ForegroundBrush}"
                                   FontWeight="Bold" />
                    </StackPanel>

                    <TextBlock TextWrapping="Wrap"
                               Margin="0 0 0 14"
                               Foreground="{DynamicResource Node.ForegroundBrush}">
                        <Run>From</Run>
                        <Run Text="{Binding SelectedConnection.Output.Node.Title}"
                             Foreground="Red" />
                        <Run> - </Run>
                        <Run Text="{Binding SelectedConnection.Output.Title}"
                             Foreground="Red" />
                        <Run>to</Run>
                        <Run Text="{Binding SelectedConnection.Input.Node.Title}"
                             Foreground="Red" />
                        <Run> - </Run>
                        <Run Text="{Binding SelectedConnection.Input.Title}"
                             Foreground="Red" />
                    </TextBlock>

                    <Button Command="{Binding SelectedConnection.DisconnectCommand}"
                            HorizontalAlignment="Left"
                            Style="{StaticResource HollowButton}"
                            Content="Delete" />
                </StackPanel>
            </Border>
            <Border CornerRadius="3"
                    Visibility="{Binding SelectedNode, Converter={shared:BooleanToVisibilityConverter}}"
                    Background="{DynamicResource PanelBackgroundBrush}"
                    BorderThickness="1"
                    BorderBrush="{DynamicResource BorderBrush}">
                <StackPanel Margin="10">
                    <StackPanel.Resources>
                        <Style TargetType="{x:Type TextBlock}"
                               BasedOn="{StaticResource {x:Type TextBlock}}">
                            <Setter Property="Margin"
                                    Value="0 0 0 5" />
                        </Style>
                    </StackPanel.Resources>

                    <StackPanel Margin="0 0 0 14">
                        <TextBlock Text="Selected node"
                                   Foreground="{DynamicResource Node.ForegroundBrush}"
                                   FontWeight="Bold" />
                    </StackPanel>

                    <TextBlock TextWrapping="Wrap"
                               Margin="0 0 0 14"
                               Foreground="{DynamicResource Node.ForegroundBrush}">
                        <Run>Title: </Run>
                        <Run Text="{Binding SelectedNode.Title}"
                             Foreground="Red" />
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap"
                               Margin="0 0 0 14"
                               Foreground="{DynamicResource Node.ForegroundBrush}">
                        <Run>Location: </Run>
                        <Run Text="{Binding SelectedNode.Location}"
                             Foreground="Red" />
                    </TextBlock>

                    <Button Command="{Binding SelectedNode.DeleteCommand}"
                            HorizontalAlignment="Left"
                            Style="{StaticResource HollowButton}"
                            Content="Delete" />
                </StackPanel>
            </Border>
        </StackPanel>
    </Grid>

</UserControl>
