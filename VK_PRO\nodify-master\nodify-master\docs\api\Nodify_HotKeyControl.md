# HotKeyControl Class  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [DispatcherObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Threading.DispatcherObject) → [DependencyObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.DependencyObject) → [Visual](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Media.Visual) → [UIElement](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.UIElement) → [FrameworkElement](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.FrameworkElement) → [Control](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Controls.Control) → [HotKeyControl](Nodify_HotKeyControl)  
  
```csharp  
public class HotKeyControl : Control  
```  
  
## Constructors  
  
### HotKeyControl()  
  
```csharp  
public HotKeyControl();  
```  
  
## Properties  
  
### Number  
  
```csharp  
public int Number { get; set; }  
```  
  
**Property Value**  
  
[Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32)  
  
