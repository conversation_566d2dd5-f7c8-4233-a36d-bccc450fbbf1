连接是由两个点之间创建的。`Source`和`Target`依赖属性是`Point`类型，通常绑定到连接器的`Anchor`点。

## 基本连接

库中所有连接的基类是`BaseConnection`，它派生自`Shape`。在创建自定义连接时，可以不受任何限值地从`BaseConnection`派生。

它公开了两个命令及其对应的事件：
- `DisconnectCommand` 及 `DisconnectEvent` - 当按住`ALT`点击连接时触发
- `SplitCommand` 及 `SplitEvent` - 当双击连接时触发

连接的`Direction`有两种值：
- `Forward`

![image](https://user-images.githubusercontent.com/12727904/192101918-af9b0da6-ecc8-48f7-bf4d-8f9fdd005153.png)
![image](https://user-images.githubusercontent.com/12727904/192101959-2cb9a837-1642-4e96-b2ef-eea5502a587f.png)

- `Backward`

![image](https://user-images.githubusercontent.com/12727904/192101941-a00e23db-07ae-49ac-a907-72e35ef67877.png)
![image](https://user-images.githubusercontent.com/12727904/192101977-1afd69f1-dab0-478e-9c3d-7d601486c289.png)

`SourceOffset`和`TargetOffset`与`OffsetMode`一起使用，会控制与锚点的距离：

![image](https://user-images.githubusercontent.com/12727904/192102096-b20887d5-b7ba-450f-9cf3-7fa4086d9637.png)

连接还有一个`Spacing`属性，会使连接在一段距离后转折到沿着从`Source`和`Target`点的方向：

- 有间距：

![image](https://user-images.githubusercontent.com/12727904/192102286-9a79da8e-5e87-4f60-9e82-979bfabcd6f3.png)

- 无间距：

![image](https://user-images.githubusercontent.com/12727904/192102302-4125b44a-dfad-4d9e-9131-efb7c17cefbe.png)

将`ArrowSize`设置为"0, 0"会移除箭头。

## 线连接

一条从`Source`到`Target`的直线。

![image](https://user-images.githubusercontent.com/12727904/192115137-d8d2145b-a769-4ee9-b4e0-8a362c94e9e7.png)

## 电路连接

有一个`Angle`依赖属性来控制转折的位置。角度以度为单位。

![image](https://user-images.githubusercontent.com/12727904/192115226-b0e515b4-5a21-46aa-956a-401f07b7d308.png)

## 曲线连接

`Source`和`Target`之间的贝塞尔曲线。

![image](https://user-images.githubusercontent.com/12727904/192115259-2fe56a68-b3e4-4f5d-aa5c-5ab83e84a84d.png)

## 预备连接

可以从连接器创建预备连接，并可以放置在`ItemContainer`或`Connector`上（如果`AllowOnlyConnectors`为false）。

预备连接的`Content`可以使用`ContentTemplate`进行自定义。如果`EnablePreview`为true，`PreviewTarget`将更新为鼠标光标下的连接器或项目容器，或者为`null`（如果没有这样的元素）。

![image](https://user-images.githubusercontent.com/12727904/192115698-fbe29101-884f-4cec-9c25-e318701d30b1.png)

预备连接的可见性可以使用`IsVisible`依赖属性进行控制。

连接器的连接捕捉可以使用`EnableSnapping`依赖属性启用。

`Source`和`Target`属性是连接器的数据上下文，预备连接完成时`Target`将更新。

还有一个`StartedCommand`，参数是`Source`，以及一个`CompletedCommand`，参数是`Target`。

> 提示：取消预备连接的方法是释放右键。