# ConnectorState.Disconnect Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<Connector\>](Nodify_Interactivity_InputElementState_TElement_) → [ConnectorState.Disconnect](Nodify_Interactivity_ConnectorState_Disconnect)  
  
**References:** [Connector](Nodify_Connector)  
  
```csharp  
public class Disconnect : InputElementState<Connector>  
```  
  
## Constructors  
  
### ConnectorState.Disconnect(Connector)  
  
```csharp  
public Disconnect(Connector connector);  
```  
  
**Parameters**  
  
`connector` [Connector](Nodify_Connector)  
  
## Methods  
  
### OnKeyDown(KeyEventArgs)  
  
```csharp  
protected override void OnKeyDown(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
### OnMouseDown(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseDown(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
### OnMouseUp(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseUp(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
