| [English](https://github.com/miroiu/nodify/wiki/Home) | [简体中文](https://github.com/miroiu/nodify/wiki/主页) |
| ----------------------------------------------------- | ------------------------------------------------------ |

---

## [主页](主页)

[开始](开始)

- [层次结构和术语](开始#层次结构和术语)
- [内容层](开始#内容层)
- [使用现有主题](开始#使用现有主题)
- [一个小案例](开始#一个小案例)
- [绘制轴网](开始#绘制轴网)

[编辑器概述](编辑器概述)

- [移动视口](编辑器概述#平移)
- [缩放视口](编辑器概述#缩放)
- [选择项目](编辑器概述#选择)
- [对齐轴网](编辑器概述#对齐)
- [命令](编辑器概述#命令)

[项目容器概述](项目容器概述)

- [选择](项目容器概述#选择)
- [移动](项目容器概述#移动和拖拽)

[节点概述](节点概述)

- [节点](节点概述#1-node-控件)
- [组节点](节点概述#2-groupingnode-控件)
- [结节点](节点概述#3-knotnode-控件)
- [状态节点](节点概述#4-statenode-控件)

[连接概述](连接概述)

- [基本连接](连接概述#基本连接)
- [预备连接](连接概述#预备连接)

[连接器概述](连接器概述)
- [NodeInput 和 NodeOutput](连接器概述#NodeInput和NodeOutput)

[剪切线概述](剪切线概述)

- [启用预览](剪切线概述#启用预览)
- [自定义连接](剪切线概述#自定义连接)
- [自定义外观](剪切线概述#自定义外观)

[小地图概述](小地图概述)

- [移动视口](小地图概述#移动视口)
- [缩放](小地图概述#缩放)
- [自定义外观](小地图概述#自定义外观)

[API 参考](API-Reference)

[(问答) 常见问题](问答)
