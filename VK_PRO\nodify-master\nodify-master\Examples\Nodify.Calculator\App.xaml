﻿<Application x:Class="Nodify.Calculator.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:nodify="https://miroiu.github.io/nodify"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/Dark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/FocusVisual.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Icons.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Dark.xaml" />

                <ResourceDictionary>

                    <Style x:Key="{x:Static SystemParameters.FocusVisualStyleKey}">
                        <Setter Property="Control.Template">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Rectangle StrokeThickness="1"
                                               StrokeDashArray="2"
                                               Margin="-2"
                                               RadiusX="3"
                                               RadiusY="3"
                                               Stroke="DodgerBlue" />
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!--WPF is wonderful-->
                    <Style x:Key="OriginalNodeInputStyle"
                           TargetType="{x:Type nodify:NodeInput}"
                           BasedOn="{StaticResource {x:Type nodify:NodeInput}}" />

                    <Style x:Key="OriginalNodeOutputStyle"
                           TargetType="{x:Type nodify:NodeOutput}"
                           BasedOn="{StaticResource {x:Type nodify:NodeOutput}}" />

                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
