name: "CodeQL"

on:
  push:
    branches: ["master", "release-v*"]
    paths:
      - Nodify/**
  pull_request:
    branches: ["master", "release-v*"]
  schedule:
    - cron: "27 6 * * 2"

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})
    runs-on: windows-latest
    permissions:
      # required for all workflows
      security-events: write

      # required to fetch internal or private CodeQL packs
      packages: read

      # only required for workflows in private repositories
      actions: read
      contents: read

    strategy:
      fail-fast: false
      matrix:
        include:
          - language: csharp
            build-mode: none

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          build-mode: ${{ matrix.build-mode }}

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{matrix.language}}"
