连接器通过引发`PendingConnectionStartedEvent`事件来创建预备连接。连接器具有一个**必须**绑定的`Anchor`依赖属性，以便在节点位置更改时实时更新它们之间的连接。`IsConnected`依赖属性**必须**设置为true，以接收`Anchor`更新。

按住`ALT`并点击连接器会触发`DisconnectCommand`。

## NodeInput 和 NodeOutput

NodeInput和NodeOutput是带有`Header`的`Connector`的实现，可以通过自定义`HeaderTemplate`来显示文本或输入框。它们还公开了一个`ConnectorTemplate`来自定义连接器本身。通常，它们与`Node.InputConnectorTemplate`和`Node.OutputConnectorTemplate`一起使用。

![image](https://user-images.githubusercontent.com/12727904/192117525-a7e1b309-70d6-4ed7-bcd7-8210dbd680ce.png)
