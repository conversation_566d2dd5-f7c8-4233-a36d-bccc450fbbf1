﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Controls.xaml" />
        <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Icons.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <local:ResizeDirectionToVisiblityConverter x:Key="ResizeDirectionToVisiblityConverter" />

    <Style x:Key="EditableTextBlockBaseStyle"
           TargetType="{x:Type local:EditableTextBlock}">
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="BorderBrush"
                Value="DodgerBlue" />
        <Setter Property="MinHeight"
                Value="{Binding FontSize, RelativeSource={RelativeSource Self}}" />
        <Setter Property="Padding"
                Value="0" />
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="VerticalContentAlignment"
                Value="Center" />
        <Setter Property="HorizontalContentAlignment"
                Value="Stretch" />
        <Setter Property="Cursor"
                Value="IBeam" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:EditableTextBlock}">
                    <Grid VerticalAlignment="{TemplateBinding VerticalAlignment}"
                          HorizontalAlignment="{TemplateBinding HorizontalAlignment}">
                        <TextBlock Text="{Binding Text, ElementName=PART_TextBox}"
                                   Background="{TemplateBinding Background}"
                                   Foreground="{TemplateBinding Foreground}"
                                   TextWrapping="{TemplateBinding TextWrapping}"
                                   Padding="{TemplateBinding Padding}"
                                   TextTrimming="{TemplateBinding TextTrimming}"
                                   VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                   HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                   x:Name="PART_Text" />

                        <TextBox Text="{Binding Text, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                 MinWidth="{TemplateBinding MinWidth}"
                                 MinHeight="{TemplateBinding MinHeight}"
                                 Background="{TemplateBinding Background}"
                                 Foreground="{TemplateBinding Foreground}"
                                 TextWrapping="{TemplateBinding TextWrapping}"
                                 BorderBrush="{TemplateBinding BorderBrush}"
                                 BorderThickness="{TemplateBinding BorderThickness}"
                                 AcceptsReturn="{TemplateBinding AcceptsReturn}"
                                 FontSize="{TemplateBinding FontSize}"
                                 Padding="{TemplateBinding Padding}"
                                 MaxLength="{TemplateBinding MaxLength}"
                                 MinLines="{TemplateBinding MinLines}"
                                 MaxLines="{TemplateBinding MinLines}"
                                 VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                 HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                 AcceptsTab="True"
                                 Visibility="Collapsed"
                                 Margin="-1"
                                 x:Name="PART_TextBox" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEditing"
                                 Value="True">
                            <Setter TargetName="PART_TextBox"
                                    Property="Visibility"
                                    Value="Visible" />
                            <Setter TargetName="PART_Text"
                                    Property="Visibility"
                                    Value="Collapsed" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:EditableTextBlock}"
           BasedOn="{StaticResource EditableTextBlockBaseStyle}" />

    <Style TargetType="{x:Type local:TabControlEx}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:TabControlEx}">
                    <Grid KeyboardNavigation.TabNavigation="Local"
                          SnapsToDevicePixels="true"
                          ClipToBounds="true">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid Panel.ZIndex="1"
                              Margin="2,2,2,0"
                              Background="{TemplateBinding Background}"
                              KeyboardNavigation.TabIndex="1">
                            <ScrollViewer x:Name="PART_ScrollViewer"
                                          HorizontalScrollBarVisibility="Auto"
                                          VerticalScrollBarVisibility="Auto">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Orientation="Horizontal"
                                                IsItemsHost="True" />
                                    <Button Style="{StaticResource IconButton}"
                                            Command="{TemplateBinding AddTabCommand}"
                                            Height="34"
                                            VerticalAlignment="Bottom"
                                            BorderBrush="{DynamicResource HighlightedBackgroundBrush}"
                                            Grid.Column="1"
                                            ToolTip="Add new tab">
                                        <Path Width="25"
                                              Height="15"
                                              Data="{StaticResource AddGeometry}"
                                              Fill="{TemplateBinding Foreground}"
                                              Stretch="Uniform" />
                                    </Button>
                                </Grid>
                            </ScrollViewer>
                        </Grid>
                        <Border x:Name="ContentPanel"
                                Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                KeyboardNavigation.TabNavigation="Local"
                                KeyboardNavigation.DirectionalNavigation="Contained"
                                KeyboardNavigation.TabIndex="2"
                                Grid.Row="1">
                            <ContentPresenter x:Name="PART_SelectedContentHost"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                              Margin="{TemplateBinding Padding}"
                                              ContentSource="SelectedContent" />
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Opacity"
                                    Value=".5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:TabItemEx}">
        <Setter Property="Background"
                Value="{DynamicResource BackgroundBrush}" />
        <Setter Property="Foreground"
                Value="{DynamicResource ForegroundBrush}" />
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleY="0" />
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin"
                Value="1 1" />
        <Setter Property="VerticalAlignment"
                Value="Bottom" />
        <Setter Property="VerticalContentAlignment"
                Value="Stretch" />
        <Setter Property="HorizontalContentAlignment"
                Value="Stretch" />
        <Setter Property="Padding"
                Value="7" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:TabItemEx}">
                    <Border x:Name="mBorder"
                            BorderThickness="1"
                            CornerRadius="5 5 0 0"
                            Background="{TemplateBinding Background}"
                            Padding="{TemplateBinding Padding}">
                        <Grid>
                            <StackPanel Orientation="Horizontal">
                                <local:EditableTextBlock Text="{TemplateBinding Header}"
                                                         Foreground="{TemplateBinding Foreground}" />
                                <Button x:Name="mCloseBtn"
                                        Command="{TemplateBinding CloseTabCommand}"
                                        CommandParameter="{TemplateBinding CloseTabCommandParameter}"
                                        Visibility="Hidden"
                                        Margin="5 0 0 0"
                                        Background="{DynamicResource HighlightedBackgroundBrush}"
                                        BorderBrush="{DynamicResource BackgroundBrush}"
                                        Style="{StaticResource IconButton}"
                                        ToolTip="Close Tab">
                                    <Path Width="16"
                                          Height="8"
                                          Data="{StaticResource CloseGeometry}"
                                          Fill="{TemplateBinding Foreground}"
                                          Stretch="Uniform" />
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Visibility"
                                    Value="Visible"
                                    TargetName="mCloseBtn" />
                            <Setter Property="Background"
                                    Value="{DynamicResource HighlightedBackgroundBrush}" />
                        </Trigger>
                        <Trigger Property="IsSelected"
                                 Value="True">
                            <Setter Property="TextElement.FontWeight"
                                    Value="Bold" />
                            <Setter Property="Background"
                                    Value="{DynamicResource HighlightedBackgroundBrush}" />
                            <Setter Property="Padding"
                                    Value="8" />
                            <Setter Property="Visibility"
                                    Value="Visible"
                                    TargetName="mCloseBtn" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Duration="0:0:.5"
                                         From="0"
                                         To="1"
                                         Storyboard.TargetProperty="(RenderTransform).(ScaleTransform.ScaleY)">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase Oscillations="1"
                                             Springiness="3"
                                             EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="{x:Type local:Swatches}">
        <Style.Resources>
            <local:MultiValueEqualityConverter x:Key="MultiValueEqualityConverter" />
            <local:ColorToSolidColorBrushConverter x:Key="ColorToSolidColorBrushConverter" />
        </Style.Resources>
        <Setter Property="Background"
                Value="#1e293b" />
        <Setter Property="Padding"
                Value="6" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:Swatches">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="8">
                        <ItemsControl ItemsSource="{TemplateBinding Colors}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="50"
                                            Padding="1"
                                            Margin="3">
                                        <Border.Style>
                                            <Style TargetType="{x:Type Border}">
                                                <Setter Property="Background"
                                                        Value="Transparent" />
                                                <Style.Triggers>
                                                    <DataTrigger Value="True">
                                                        <DataTrigger.Binding>
                                                            <MultiBinding Converter="{StaticResource MultiValueEqualityConverter}">
                                                                <Binding RelativeSource="{RelativeSource AncestorType=local:Swatches}"
                                                                         Path="SelectedColor"
                                                                         Mode="OneWay" />
                                                                <Binding Mode="OneWay" />
                                                            </MultiBinding>
                                                        </DataTrigger.Binding>
                                                        <Setter Property="Background"
                                                                Value="White" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <Ellipse Width="24"
                                                 Height="24"
                                                 Stroke="#1e293b"
                                                 Fill="{Binding ., Converter={StaticResource ColorToSolidColorBrushConverter}}"
                                                 Cursor="Hand" />
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled"
                     Value="False">
                <Setter Property="Opacity"
                        Value="0.7" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="{x:Type local:ResizablePanel}">
        <Setter Property="BorderBrush"
                Value="#1e293b" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:ResizablePanel">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid>
                            <ContentPresenter Margin="{TemplateBinding Padding}" />

                            <local:Resizer Direction="TopLeft"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Top"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=TopLeft}" />

                            <local:Resizer Direction="TopRight"
                                           HorizontalAlignment="Right"
                                           VerticalAlignment="Top"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=TopRight}" />

                            <local:Resizer Direction="BottomLeft"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Bottom"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=BottomLeft}" />

                            <local:Resizer Direction="BottomRight"
                                           HorizontalAlignment="Right"
                                           VerticalAlignment="Bottom"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=BottomRight}" />

                            <local:Resizer Direction="Left"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=Left}" />

                            <local:Resizer Direction="Right"
                                           HorizontalAlignment="Right"
                                           VerticalAlignment="Center"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=Right}" />

                            <local:Resizer Direction="Top"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Top"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=Top}" />

                            <local:Resizer Direction="Bottom"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Bottom"
                                           Visibility="{TemplateBinding Directions, Converter={StaticResource ResizeDirectionToVisiblityConverter}, ConverterParameter=Bottom}" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:Resizer}">
        <Setter Property="Background"
                Value="#1e293b" />
        <Setter Property="Width"
                Value="6" />
        <Setter Property="Height"
                Value="6" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="local:Resizer">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}" />
                    <ControlTemplate.Triggers>
                        <Trigger Property="Direction"
                                 Value="TopLeft">
                            <Setter Property="Cursor"
                                    Value="SizeNWSE" />
                            <Setter Property="Margin"
                                    Value="-4 -4 0 0" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="TopRight">
                            <Setter Property="Cursor"
                                    Value="SizeNESW" />
                            <Setter Property="Margin"
                                    Value="0 -4 -4 0" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="BottomLeft">
                            <Setter Property="Cursor"
                                    Value="SizeNESW" />
                            <Setter Property="Margin"
                                    Value="-4 0 0 -4" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="BottomRight">
                            <Setter Property="Cursor"
                                    Value="SizeNWSE" />
                            <Setter Property="Margin"
                                    Value="0 0 -4 -4" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="Left">
                            <Setter Property="Cursor"
                                    Value="SizeWE" />
                            <Setter Property="Margin"
                                    Value="-4 0 0 0" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="Right">
                            <Setter Property="Cursor"
                                    Value="SizeWE" />
                            <Setter Property="Margin"
                                    Value="0 0 -4 0" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="Top">
                            <Setter Property="Cursor"
                                    Value="SizeNS" />
                            <Setter Property="Margin"
                                    Value="0 -4 0 0" />
                        </Trigger>
                        <Trigger Property="Direction"
                                 Value="Bottom">
                            <Setter Property="Cursor"
                                    Value="SizeNS" />
                            <Setter Property="Margin"
                                    Value="0 0 0 -4" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>