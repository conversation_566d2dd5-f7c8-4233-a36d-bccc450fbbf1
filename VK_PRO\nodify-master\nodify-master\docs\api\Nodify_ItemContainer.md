# ItemContainer Class  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [DispatcherObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Threading.DispatcherObject) → [DependencyObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.DependencyObject) → [Visual](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Media.Visual) → [UIElement](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.UIElement) → [FrameworkElement](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.FrameworkElement) → [Control](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Controls.Control) → [ContentControl](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Controls.ContentControl) → [ItemContainer](Nodify_ItemContainer)  
  
**Implements:** [INodifyCanvasItem](Nodify_INodifyCanvasItem), [IKeyboardFocusTarget\<ItemContainer\>](Nodify_Interactivity_IKeyboardFocusTarget_TElement_)  
  
**References:** [Connector](Nodify_Connector), [ContainerState.Default](Nodify_Interactivity_ContainerState_Default), [EditorCommands](Nodify_EditorCommands), [GroupingNode](Nodify_GroupingNode), [InputProcessor](Nodify_Interactivity_InputProcessor), [ItemsMovedEventArgs](Nodify_Events_ItemsMovedEventArgs), [NodifyEditor](Nodify_NodifyEditor), [PendingConnection](Nodify_PendingConnection), [PreviewLocationChanged](Nodify_Events_PreviewLocationChanged), [SelectionType](Nodify_SelectionType)  
  
The container for all the items generated by the [ItemsControl.ItemsSource](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Controls.ItemsControl#itemssource) of the [NodifyEditor](Nodify_NodifyEditor).  
  
```csharp  
public class ItemContainer : ContentControl, INodifyCanvasItem, IKeyboardFocusTarget<ItemContainer>  
```  
  
## Constructors  
  
### ItemContainer(NodifyEditor)  
  
Constructs an instance of an [ItemContainer](Nodify_ItemContainer) in the specified [NodifyEditor](Nodify_NodifyEditor).  
  
```csharp  
public ItemContainer(NodifyEditor editor);  
```  
  
**Parameters**  
  
`editor` [NodifyEditor](Nodify_NodifyEditor)  
  
## Fields  
  
### IsPreviewingSelectionPropertyKey  
  
```csharp  
protected static DependencyPropertyKey IsPreviewingSelectionPropertyKey;  
```  
  
**Field Value**  
  
[DependencyPropertyKey](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.DependencyPropertyKey)  
  
## Properties  
  
### ActualSize  
  
Gets the actual size of this [ItemContainer](Nodify_ItemContainer).  
  
```csharp  
public Size ActualSize { get; set; }  
```  
  
**Property Value**  
  
[Size](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Size)  
  
### Bounds  
  
Gets the bounds of the selection area for this [ItemContainer](Nodify_ItemContainer) based on its [ItemContainer.Location](Nodify_ItemContainer#location) and [ItemContainer.DesiredSizeForSelection](Nodify_ItemContainer#desiredsizeforselection).  
  
```csharp  
public virtual Rect Bounds { get; set; }  
```  
  
**Property Value**  
  
[Rect](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Rect)  
  
### DesiredSizeForSelection  
  
Overrides the size to check against when calculating if this [ItemContainer](Nodify_ItemContainer) can be part of the current [NodifyEditor.SelectedArea](Nodify_NodifyEditor#selectedarea).
            Defaults to [UIElement.RenderSize](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.UIElement#rendersize).  
  
```csharp  
public Size? DesiredSizeForSelection { get; set; }  
```  
  
**Property Value**  
  
[Size?](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1)  
  
### Editor  
  
The [NodifyEditor](Nodify_NodifyEditor) that owns this [ItemContainer](Nodify_ItemContainer).  
  
```csharp  
public NodifyEditor Editor { get; set; }  
```  
  
**Property Value**  
  
[NodifyEditor](Nodify_NodifyEditor)  
  
### HasContextMenu  
  
Gets a value indicating whether the container has a context menu.  
  
```csharp  
public bool HasContextMenu { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### HasCustomContextMenu  
  
Gets or sets a value indicating whether the container uses a custom context menu.  
  
```csharp  
public bool HasCustomContextMenu { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### HighlightBrush  
  
Gets or sets the brush used when the [PendingConnection.IsOverElementProperty](Nodify_PendingConnection#isoverelementproperty) attached property is true for this [ItemContainer](Nodify_ItemContainer).  
  
```csharp  
public Brush HighlightBrush { get; set; }  
```  
  
**Property Value**  
  
[Brush](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Media.Brush)  
  
### InputProcessor  
  
```csharp  
protected InputProcessor InputProcessor { get; set; }  
```  
  
**Property Value**  
  
[InputProcessor](Nodify_Interactivity_InputProcessor)  
  
### IsDraggable  
  
Gets or sets whether this [ItemContainer](Nodify_ItemContainer) can be dragged.  
  
```csharp  
public bool IsDraggable { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### IsPreviewingLocation  
  
Gets a value indicating whether this [ItemContainer](Nodify_ItemContainer) is previewing a new location but didn't logically move there.  
  
```csharp  
public bool IsPreviewingLocation { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### IsPreviewingSelection  
  
Gets a value indicating whether this [ItemContainer](Nodify_ItemContainer) is about to change its [ItemContainer.IsSelected](Nodify_ItemContainer#isselected) state.  
  
```csharp  
public Boolean? IsPreviewingSelection { get; set; }  
```  
  
**Property Value**  
  
[Boolean?](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1)  
  
### IsSelectable  
  
Gets or sets whether this [ItemContainer](Nodify_ItemContainer) can be selected.  
  
```csharp  
public bool IsSelectable { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### IsSelected  
  
Gets or sets a value that indicates whether this [ItemContainer](Nodify_ItemContainer) is selected.
            Can only be set if [ItemContainer.IsSelectable](Nodify_ItemContainer#isselectable) is true.  
  
```csharp  
public bool IsSelected { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### Location  
  
Gets or sets the location of this [ItemContainer](Nodify_ItemContainer) inside the [NodifyEditor](Nodify_NodifyEditor) in graph space coordinates.  
  
```csharp  
public virtual Point Location { get; set; }  
```  
  
**Property Value**  
  
[Point](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Point)  
  
### PreserveSelectionOnRightClick  
  
Indicates whether right-click on the container should preserve the current selection.  
  
```csharp  
public static bool PreserveSelectionOnRightClick { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
### SelectedBorderThickness  
  
Gets or sets the border thickness used when [ItemContainer.IsSelected](Nodify_ItemContainer#isselected) or [ItemContainer.IsPreviewingSelection](Nodify_ItemContainer#ispreviewingselection) is true.  
  
```csharp  
public Thickness SelectedBorderThickness { get; set; }  
```  
  
**Property Value**  
  
[Thickness](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Thickness)  
  
### SelectedBrush  
  
Gets or sets the brush used when [ItemContainer.IsSelected](Nodify_ItemContainer#isselected) or [ItemContainer.IsPreviewingSelection](Nodify_ItemContainer#ispreviewingselection) is true.  
  
```csharp  
public Brush SelectedBrush { get; set; }  
```  
  
**Property Value**  
  
[Brush](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Media.Brush)  
  
### SelectedMargin  
  
The calculated margin when the container is selected or previewing selection.  
  
```csharp  
public Thickness SelectedMargin { get; set; }  
```  
  
**Property Value**  
  
[Thickness](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Thickness)  
  
## Methods  
  
### BeginDragging()  
  
```csharp  
public void BeginDragging();  
```  
  
### CancelDragging()  
  
```csharp  
public void CancelDragging();  
```  
  
### EndDragging()  
  
```csharp  
public void EndDragging();  
```  
  
### IsSelectableInArea(Rect, Boolean)  
  
Checks if area contains or intersects with this [ItemContainer](Nodify_ItemContainer) taking into consideration the [ItemContainer.DesiredSizeForSelection](Nodify_ItemContainer#desiredsizeforselection).  
  
```csharp  
public virtual bool IsSelectableInArea(Rect area, bool isContained);  
```  
  
**Parameters**  
  
`area` [Rect](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Rect): The area to check if contains or intersects this [ItemContainer](Nodify_ItemContainer).  
  
`isContained` [Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean): If true will check if area contains this, otherwise will check if area intersects with this.  
  
**Returns**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean): True if area contains or intersects this [ItemContainer](Nodify_ItemContainer).  
  
### OnKeyDown(KeyEventArgs)  
  
```csharp  
protected override void OnKeyDown(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
### OnKeyUp(KeyEventArgs)  
  
```csharp  
protected override void OnKeyUp(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
### OnLocationChanged()  
  
Raises the [ItemContainer.LocationChangedEvent](Nodify_ItemContainer#locationchangedevent) and sets [ItemContainer.IsPreviewingLocation](Nodify_ItemContainer#ispreviewinglocation) to false.  
  
```csharp  
protected void OnLocationChanged();  
```  
  
### OnLostMouseCapture(MouseEventArgs)  
  
```csharp  
protected override void OnLostMouseCapture(MouseEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseEventArgs)  
  
### OnMouseDown(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseDown(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
### OnMouseMove(MouseEventArgs)  
  
```csharp  
protected override void OnMouseMove(MouseEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseEventArgs)  
  
### OnMouseUp(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseUp(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseButtonEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
### OnMouseWheel(MouseWheelEventArgs)  
  
```csharp  
protected override void OnMouseWheel(MouseWheelEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseWheelEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseWheelEventArgs)  
  
### OnRenderSizeChanged(SizeChangedInfo)  
  
```csharp  
protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo);  
```  
  
**Parameters**  
  
`sizeInfo` [SizeChangedInfo](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.SizeChangedInfo)  
  
### OnSelectedChanged(Boolean)  
  
Raises the [ItemContainer.SelectedEvent](Nodify_ItemContainer#selectedevent) or [ItemContainer.UnselectedEvent](Nodify_ItemContainer#unselectedevent) based on newValue.
            Called when the [ItemContainer.IsSelected](Nodify_ItemContainer#isselected) value is changed.  
  
```csharp  
protected void OnSelectedChanged(bool newValue);  
```  
  
**Parameters**  
  
`newValue` [Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean): True if selected, false otherwise.  
  
### OnVisualParentChanged(DependencyObject)  
  
```csharp  
protected override void OnVisualParentChanged(DependencyObject oldParent);  
```  
  
**Parameters**  
  
`oldParent` [DependencyObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.DependencyObject)  
  
### Select(SelectionType)  
  
Modifies the selection state of the current item based on the specified selection type.  
  
```csharp  
public void Select(SelectionType type);  
```  
  
**Parameters**  
  
`type` [SelectionType](Nodify_SelectionType): The type of selection to perform.  
  
### UpdateDragging(Vector)  
  
```csharp  
public void UpdateDragging(Vector amount);  
```  
  
**Parameters**  
  
`amount` [Vector](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Vector)  
  
## Events  
  
### LocationChanged  
  
Occurs when the [ItemContainer.Location](Nodify_ItemContainer#location) of this [ItemContainer](Nodify_ItemContainer) is changed.  
  
```csharp  
public event RoutedEventHandler LocationChanged;  
```  
  
**Event Type**  
  
[RoutedEventHandler](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.RoutedEventHandler)  
  
### PreviewLocationChanged  
  
Occurs when the [ItemContainer](Nodify_ItemContainer) is previewing a new location.  
  
```csharp  
public event PreviewLocationChanged PreviewLocationChanged;  
```  
  
**Event Type**  
  
[PreviewLocationChanged](Nodify_Events_PreviewLocationChanged)  
  
### Selected  
  
Occurs when this [ItemContainer](Nodify_ItemContainer) is selected.  
  
```csharp  
public event RoutedEventHandler Selected;  
```  
  
**Event Type**  
  
[RoutedEventHandler](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.RoutedEventHandler)  
  
### Unselected  
  
Occurs when this [ItemContainer](Nodify_ItemContainer) is unselected.  
  
```csharp  
public event RoutedEventHandler Unselected;  
```  
  
**Event Type**  
  
[RoutedEventHandler](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.RoutedEventHandler)  
  
