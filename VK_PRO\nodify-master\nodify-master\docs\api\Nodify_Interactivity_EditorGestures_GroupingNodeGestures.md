# EditorGestures.GroupingNodeGestures Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [EditorGestures.GroupingNodeGestures](Nodify_Interactivity_EditorGestures_GroupingNodeGestures)  
  
**References:** [EditorGestures](Nodify_Interactivity_EditorGestures), [InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
```csharp  
public class GroupingNodeGestures  
```  
  
## Constructors  
  
### EditorGestures.GroupingNodeGestures()  
  
```csharp  
public GroupingNodeGestures();  
```  
  
## Properties  
  
### SwitchMovementMode  
  
```csharp  
public ModifierKeys SwitchMovementMode { get; set; }  
```  
  
**Property Value**  
  
[ModifierKeys](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.ModifierKeys)  
  
### ToggleContentSelection  
  
```csharp  
public InputGestureRef ToggleContentSelection { get; set; }  
```  
  
**Property Value**  
  
[InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
## Methods  
  
### Apply(EditorGestures.GroupingNodeGestures)  
  
```csharp  
public void Apply(EditorGestures.GroupingNodeGestures gestures);  
```  
  
**Parameters**  
  
`gestures` [EditorGestures.GroupingNodeGestures](Nodify_Interactivity_EditorGestures_GroupingNodeGestures)  
  
### Unbind()  
  
```csharp  
public void Unbind();  
```  
  
