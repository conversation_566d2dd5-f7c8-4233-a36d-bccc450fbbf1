{"format": 1, "restore": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.StateMachine\\Nodify.StateMachine.csproj": {}}, "projects": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj", "projectName": "Nodify.Shared", "projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net48", "net5-windows", "net6-windows", "net8-windows", "net9-windows", "netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\SICK\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}, "net5.0-windows7.0": {"targetAlias": "net5-windows", "projectReferences": {}}, "net6.0-windows7.0": {"targetAlias": "net6-windows", "projectReferences": {}}, "net8.0-windows7.0": {"targetAlias": "net8-windows", "projectReferences": {}}, "net9.0-windows7.0": {"targetAlias": "net9-windows", "projectReferences": {}}, "net472": {"targetAlias": "net472", "projectReferences": {}}, "net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net5.0-windows7.0": {"targetAlias": "net5-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net6.0-windows7.0": {"targetAlias": "net6-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0-windows7.0": {"targetAlias": "net8-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-windows7.0": {"targetAlias": "net9-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.StateMachine\\Nodify.StateMachine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.StateMachine\\Nodify.StateMachine.csproj", "projectName": "Nodify.StateMachine", "projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.StateMachine\\Nodify.StateMachine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.StateMachine\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net48", "net5-windows", "net6-windows", "net8-windows", "net9-windows", "netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\SICK\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}, "net5.0-windows7.0": {"targetAlias": "net5-windows", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}, "net6.0-windows7.0": {"targetAlias": "net6-windows", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}, "net8.0-windows7.0": {"targetAlias": "net8-windows", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}, "net9.0-windows7.0": {"targetAlias": "net9-windows", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}, "net472": {"targetAlias": "net472", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}, "net48": {"targetAlias": "net48", "projectReferences": {"D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Examples\\Nodify.Shared\\Nodify.Shared.csproj"}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net5.0-windows7.0": {"targetAlias": "net5-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net6.0-windows7.0": {"targetAlias": "net6-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0-windows7.0": {"targetAlias": "net8-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-windows7.0": {"targetAlias": "net9-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}, "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj": {"version": "7.1.0", "restore": {"projectUniqueName": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj", "projectName": "Nodify", "projectPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\Nodify.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AITS_VERSION_PRO\\VK_PRO\\nodify-master\\Nodify\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472", "net48", "net5-windows", "net6-windows", "net8-windows", "net9-windows", "netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\SICK\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}, "net5.0-windows7.0": {"targetAlias": "net5-windows", "projectReferences": {}}, "net6.0-windows7.0": {"targetAlias": "net6-windows", "projectReferences": {}}, "net8.0-windows7.0": {"targetAlias": "net8-windows", "projectReferences": {}}, "net9.0-windows7.0": {"targetAlias": "net9-windows", "projectReferences": {}}, "net472": {"targetAlias": "net472", "projectReferences": {}}, "net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net5.0-windows7.0": {"targetAlias": "net5-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net6.0-windows7.0": {"targetAlias": "net6-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0-windows7.0": {"targetAlias": "net8-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-windows7.0": {"targetAlias": "net9-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net472": {"targetAlias": "net472", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}