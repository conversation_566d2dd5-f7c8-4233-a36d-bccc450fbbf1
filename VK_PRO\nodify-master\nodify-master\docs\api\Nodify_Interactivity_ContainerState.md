# ContainerState Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [ContainerState](Nodify_Interactivity_ContainerState)  
  
```csharp  
public static class ContainerState  
```  
  
## Properties  
  
### EnableToggledDraggingMode  
  
Determines whether toggled dragging mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledDraggingMode { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
