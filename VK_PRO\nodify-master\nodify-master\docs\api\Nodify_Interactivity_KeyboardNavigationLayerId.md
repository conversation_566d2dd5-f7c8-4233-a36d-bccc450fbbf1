# KeyboardNavigationLayerId Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [KeyboardNavigationLayerId](Nodify_Interactivity_KeyboardNavigationLayerId)  
  
**References:** [ConnectionsMultiSelector](Nodify_ConnectionsMultiSelector), [DecoratorsControl](Nodify_DecoratorsControl), [IKeyboardNavigationLayer](Nodify_Interactivity_IKeyboardNavigationLayer), [IKeyboardNavigationLayerGroup](Nodify_Interactivity_IKeyboardNavigationLayerGroup), [NodifyEditor](Nodify_NodifyEditor)  
  
Represents a unique identifier for a keyboard navigation layer.  
  
```csharp  
public class KeyboardNavigationLayerId  
```  
  
## Constructors  
  
### KeyboardNavigationLayerId()  
  
```csharp  
public KeyboardNavigationLayerId();  
```  
  
## Fields  
  
### Connections  
  
```csharp  
public static KeyboardNavigationLayerId Connections;  
```  
  
**Field Value**  
  
[KeyboardNavigationLayerId](Nodify_Interactivity_KeyboardNavigationLayerId)  
  
### Decorators  
  
```csharp  
public static KeyboardNavigationLayerId Decorators;  
```  
  
**Field Value**  
  
[KeyboardNavigationLayerId](Nodify_Interactivity_KeyboardNavigationLayerId)  
  
### Nodes  
  
```csharp  
public static KeyboardNavigationLayerId Nodes;  
```  
  
**Field Value**  
  
[KeyboardNavigationLayerId](Nodify_Interactivity_KeyboardNavigationLayerId)  
  
