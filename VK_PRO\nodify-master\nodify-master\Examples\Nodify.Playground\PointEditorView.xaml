﻿<UserControl x:Class="Nodify.Playground.PointEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Nodify.Playground"
             d:DataContext="{d:DesignInstance Type={x:Type local:PointEditor}, IsDesignTimeCreatable=True}"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"
                              MinWidth="20" />
            <ColumnDefinition MinWidth="30" />
        </Grid.ColumnDefinitions>

        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <TextBlock TextAlignment="Center"
                   VerticalAlignment="Center">
                <Run Text="{Binding XLabel}" />
                <Run Text=":" />
        </TextBlock>
        <TextBox Text="{Binding X, Mode=TwoWay}"
                 Grid.Column="1" />

        <TextBlock TextAlignment="Center"
                   VerticalAlignment="Center"
                   Grid.Row="1"
                   Margin="0 5 0 0">
                <Run Text="{Binding YLabel}" />
                <Run Text=":" />
        </TextBlock>
        <TextBox Text="{Binding Y, Mode=TwoWay}"
                 Margin="0 5 0 0"
                 Grid.Row="1"
                 Grid.Column="1" />
    </Grid>
</UserControl>
