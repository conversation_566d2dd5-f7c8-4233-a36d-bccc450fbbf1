# MinimapItem Class  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [DispatcherObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Threading.DispatcherObject) → [DependencyObject](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.DependencyObject) → [Visual](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Media.Visual) → [UIElement](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.UIElement) → [FrameworkElement](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.FrameworkElement) → [Control](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Controls.Control) → [ContentControl](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Controls.ContentControl) → [MinimapItem](Nodify_MinimapItem)  
  
**References:** [Minimap](Nodify_Minimap)  
  
```csharp  
public class MinimapItem : ContentControl  
```  
  
## Constructors  
  
### MinimapItem()  
  
```csharp  
public MinimapItem();  
```  
  
## Properties  
  
### Location  
  
Gets or sets the location of this [MinimapItem](Nodify_MinimapItem) inside the [Minimap](Nodify_Minimap).  
  
```csharp  
public Point Location { get; set; }  
```  
  
**Property Value**  
  
[Point](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Point)  
  
