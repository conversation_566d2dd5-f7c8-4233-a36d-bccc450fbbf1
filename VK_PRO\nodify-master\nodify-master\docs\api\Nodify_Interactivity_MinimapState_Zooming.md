# MinimapState.Zooming Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<Minimap\>](Nodify_Interactivity_InputElementState_TElement_) → [MinimapState.Zooming](Nodify_Interactivity_MinimapState_Zooming)  
  
**References:** [Minimap](Nodify_Minimap)  
  
```csharp  
public class Zooming : InputElementState<Minimap>  
```  
  
## Constructors  
  
### MinimapState.Zooming(Minimap)  
  
```csharp  
public Zooming(Minimap minimap);  
```  
  
**Parameters**  
  
`minimap` [Minimap](Nodify_Minimap)  
  
## Methods  
  
### OnKeyDown(KeyEventArgs)  
  
```csharp  
protected override void OnKeyDown(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
### OnMouseWheel(MouseWheelEventArgs)  
  
```csharp  
protected override void OnMouseWheel(MouseWheelEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseWheelEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseWheelEventArgs)  
  
