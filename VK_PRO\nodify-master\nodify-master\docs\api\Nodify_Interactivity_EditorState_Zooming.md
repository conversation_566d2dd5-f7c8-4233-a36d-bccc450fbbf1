# EditorState.Zooming Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<NodifyEditor\>](Nodify_Interactivity_InputElementState_TElement_) → [EditorState.Zooming](Nodify_Interactivity_EditorState_Zooming)  
  
**References:** [NodifyEditor](Nodify_NodifyEditor)  
  
```csharp  
public class Zooming : InputElementState<NodifyEditor>  
```  
  
## Constructors  
  
### EditorState.Zooming(NodifyEditor)  
  
```csharp  
public Zooming(NodifyEditor editor);  
```  
  
**Parameters**  
  
`editor` [NodifyEditor](Nodify_NodifyEditor)  
  
## Methods  
  
### OnMouseWheel(MouseWheelEventArgs)  
  
```csharp  
protected override void OnMouseWheel(MouseWheelEventArgs e);  
```  
  
**Parameters**  
  
`e` [MouseWheelEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseWheelEventArgs)  
  
