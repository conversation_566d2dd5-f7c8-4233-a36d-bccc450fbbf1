## 目录

- [启用预览](#启用预览)
- [自定义连接](#自定义连接)
- [自定义外观](#自定义外观)

`CuttingLine` 控件是一个自定义控件，用于移除与其相交的连接线。

默认的剪切启动手势是 `SHIFT+ALT+左键单击` ，可以通过设置 `EditorGestures.Editor.Cutting` 手势进行配置。剪切操作可以通过按下 `Escape` 键或右键取消，右键的配置同样可以通过设置 `EditorGestures.Editor.CancelAction` 手势进行配置。

在 `NodifyEditor` 中可用的相关命令有：:

- CuttingStartedCommand
- CuttingCompletedCommand
- RemoveConnectionCommand - 当剪切操作完成时，对每个相交的连接线调用

### 启用预览

若希望连接线在与剪切线相交时改变样式，需要将 `NodifyEditor.EnableCuttingLinePreview` 设置为 `true`。

> [!警告]
> 根据连接线数量及其几何复杂度，此操作可能对性能造成较大影响。

![cutting](https://github.com/user-attachments/assets/22f705c8-3bf1-466b-8bbd-da007f30deb2)

## 自定义连接

若希望剪切自定义连接线，需要将连接类型添加到 `NodifyEditor.CuttingConnectionTypes`:

```csharp
// 示例：将 Line 形状添加到可剪切的连接类型中
NodifyEditor.CuttingConnectionTypes.Add(typeof(System.Windows.Shapes.Line));
```

可以如下自定义与剪切线相交的自定义连接线样式：

```xml
<Style TargetType="Line">
    <Style.Triggers>
        <Trigger Property="nodify:CuttingLine.IsOverElement" Value="True">
            <Setter Property="Opacity"
                    Value="0.4" />
        </Trigger>
    </Style.Triggers>
</Style>
```

## 自定义外观

可使用 `CuttingLineStyle` 自定义剪切线的样式：

```xml
<Style x:Key="CuttingLineStyle"
       TargetType="{x:Type nodify:CuttingLine}"
       BasedOn="{StaticResource {x:Type nodify:CuttingLine}}">
    <Setter Property="StrokeDashArray"
            Value="1 1" />
    <Setter Property="StrokeThickness"
            Value="2" />
</Style>

<nodify:NodifyEditor CuttingLineStyle="{StaticResource CuttingLineStyle}" ... />
```
