
#### 问：为什么我移动节点时连接没有跟随连接器？

答1：您没有绑定连接器的`Anchor`属性，或者连接的`Source`和`Target`属性。

答2：仅当连接器的`IsConnected`属性设置为true时，`Anchor`才会更新。

***

#### 问：我可以更改鼠标/键盘绑定吗？

答：可以！您可以根据喜好配置[编辑器手势](https://github.com/miroiu/nodify/blob/master/Nodify/EditorGestures.cs)。

***

#### 问：有Avalonia移植版本吗？

答：有一个非常棒的Avalonia移植版本！您可以在[这里](https://github.com/BAndysc/nodify-avalonia)查看。非常感谢[BAndysc](https://github.com/BAndysc)使这一切成为可能。

***

#### 问：有没有非MVVM的方法添加节点和连接？

答：没有。

***

#### 问：如何设置选中的节点始终在顶部？

答：[链接](https://github.com/miroiu/nodify/issues/57)

***

#### 问：如何更改节点的大小？

答：[链接](https://github.com/miroiu/nodify/issues/55)

***

#### 问：如何限制X和Y方向的平移？

答：[链接](https://github.com/miroiu/nodify/issues/53)

***

#### 问：视口滞后。如何修复？

答：[链接](https://github.com/miroiu/nodify/issues/60)

***

#### 问：如何将节点从工具箱拖入编辑器？

答：[链接](https://github.com/miroiu/nodify/issues/81)