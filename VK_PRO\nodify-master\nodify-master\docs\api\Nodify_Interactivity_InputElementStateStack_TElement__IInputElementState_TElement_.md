# InputElementStateStack\<TElement\>.IInputElementState\<TElement\> Interface  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Implements:** [IInputHandler](Nodify_Interactivity_IInputHandler)  
  
```csharp  
public interface IInputElementState<TElement> : IInput<PERSON>and<PERSON>  
```  
  
## Methods  
  
### Enter(InputElementStateStack\<TElement\>.IInputElementState\<TElement\>)  
  
```csharp  
public virtual void Enter(InputElementStateStack<TElement>.IInputElementState<TElement> from);  
```  
  
**Parameters**  
  
`from` [InputElementStateStack\<TElement\>.IInputElementState\<TElement\>](Nodify_Interactivity_InputElementStateStack_TElement__IInputElementState_TElement_)  
  
### Exit()  
  
```csharp  
public virtual void Exit();  
```  
  
