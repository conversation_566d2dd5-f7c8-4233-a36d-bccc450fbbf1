节点是节点编辑器的基本组件。它们被包装在[`ItemContainer`](项目容器概述)中，并且可以是任何自定义控件。（例如，TextBlock）

以下是库中包含的节点：

### 1. ```Node``` 控件
这种类型的节点支持```Input```和```Output```连接器，可以随意移动。

```xml
<nodify:NodifyEditor xmlns:sys="clr-namespace:System;assembly=mscorlib">
  <nodify:NodifyEditor.ItemsSource>
    <CompositeCollection>
        <nodify:Node Header="My Node">
            <nodify:Node.Input>
                <CompositeCollection>
                    <sys:String>In 0</sys:String>
                    <sys:String>In 1</sys:String>
                </CompositeCollection>
            </nodify:Node.Input>
            <nodify:Node.Output>
                <CompositeCollection>
                    <sys:String>Out 0</sys:String>
                    <sys:String>Out 1</sys:String>
                </CompositeCollection>
            </nodify:Node.Output>
            <nodify:Node.InputConnectorTemplate>
                <DataTemplate>
                    <nodify:NodeInput Header="{Binding}" />
                </DataTemplate>
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <DataTemplate>
                    <nodify:NodeOutput Header="{Binding}" />
                </DataTemplate>
            </nodify:Node.OutputConnectorTemplate>
        </nodify:Node>
    </CompositeCollection>
  </nodify:NodifyEditor.ItemsSource>
</nodify:NodifyEditor>
```

节点的`Header`可以使用`HeaderTemplate`进行自定义。同样，节点的`Footer`可以使用`FooterTemplate`进行自定义。

`Input`集合中的每个项目可以使用`InputConnectorTemplate`进行自定义。同样，`Output`可以使用`OutputConnectorTemplate`进行自定义。

![Node](https://i.imgur.com/VwAYlX3.gif)

### 2. ```GroupingNode``` 控件

这种类型的节点可以调整大小，如果通过```Header```拖动，它将移动其内部的节点。

如果按住```SwitchMovementModeModifierKey```（默认情况下为**Shift**键），它将移动而不会移动其子节点。

```xml
<nodify:NodifyEditor>
    <nodify:NodifyEditor.ItemsSource>
        <CompositeCollection>
            <nodify:GroupingNode Header="Grouping node"
                            Width="300"
                            Height="250" />            
            <nodify:Node Header="My node" />
            <nodify:Node Header="My other node" />
        </CompositeCollection>
    </nodify:NodifyEditor.ItemsSource>
</nodify:NodifyEditor>
```

节点的`Header`可以使用`HeaderTemplate`进行自定义。同样，节点的`Content`可以使用`ContentTemplate`进行自定义。

节点的大小可以通过更改`ActualSize`依赖属性的值来编程设置。

#### 默认值

* CanResize: true
* MovementMode: Grouped

#### 命令

* ResizeCompleted
* ResizeStarted

![Grouping Node](https://i.imgur.com/HYxt2cs.gif)

### 3. ```KnotNode``` 控件

这种类型的控件可以用于重新排布（reroute）```Connection```，因为它只支持一个```Connector```。

节点的`Content`可以使用`ContentTemplate`进行自定义。

```xml
<nodify:NodifyEditor>
    <nodify:NodifyEditor.ItemsSource>
        <CompositeCollection>
            <nodify:KnotNode />
        </CompositeCollection>
    </nodify:NodifyEditor.ItemsSource>
</nodify:NodifyEditor>
```

![Knot Node](https://i.imgur.com/fMrEqY1.gif)

### 4. ```StateNode``` 控件

这种类型的节点本身就是一个```Connector```，这意味着它将在交互时引发```PendingConnection```事件。由于它继承自`Connector`，你需要将`Anchor`属性和`IsConnected`绑定到相应的状态。（如果`IsConnected`设置为false，连接将不会更新）

```xml
<nodify:NodifyEditor>
    <nodify:NodifyEditor.ItemsSource>
        <CompositeCollection>
            <nodify:StateNode Content="My node" />
        </CompositeCollection>
    </nodify:NodifyEditor.ItemsSource>
</nodify:NodifyEditor>
```

节点的`Content`可以使用`ContentTemplate`进行自定义。

![State Node](https://i.imgur.com/FrI2epL.gif)