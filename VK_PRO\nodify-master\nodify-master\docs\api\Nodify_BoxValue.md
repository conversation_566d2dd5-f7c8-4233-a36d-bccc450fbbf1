# BoxValue Class  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [BoxValue](Nodify_BoxValue)  
  
```csharp  
public static class BoxValue  
```  
  
## Fields  
  
### ArrowSize  
  
```csharp  
public static object ArrowSize;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### ConnectionOffset  
  
```csharp  
public static object ConnectionOffset;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Double0  
  
```csharp  
public static object Double0;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Double1  
  
```csharp  
public static object Double1;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Double1000  
  
```csharp  
public static object Double1000;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Double2  
  
```csharp  
public static object Double2;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Double45  
  
```csharp  
public static object Double45;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Double5  
  
```csharp  
public static object Double5;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### DoubleHalf  
  
```csharp  
public static object DoubleHalf;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### False  
  
```csharp  
public static object False;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Int0  
  
```csharp  
public static object Int0;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Int1  
  
```csharp  
public static object Int1;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Point  
  
```csharp  
public static object Point;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Rect  
  
```csharp  
public static object Rect;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Size  
  
```csharp  
public static object Size;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### Thickness2  
  
```csharp  
public static object Thickness2;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### True  
  
```csharp  
public static object True;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### UInt0  
  
```csharp  
public static object UInt0;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
### UInt1  
  
```csharp  
public static object UInt1;  
```  
  
**Field Value**  
  
[Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object)  
  
