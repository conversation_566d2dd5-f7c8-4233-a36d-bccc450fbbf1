```项目容器（ItemContainer）``` 是编辑器中**最重要**的部分。它是一个内容控件，用于包装由 ```编辑器（NodifyEditor）``` 的 ```ItemsSource``` 生成的每个控件，并在图形坐标中具有 `Location`属性。

## 目录
 - [选择](#选择)
   - [API](#选择-api)
 - [移动](#移动和拖拽)

## 选择

选择一个 ```ItemContainer``` 通过在容器的边界框上释放左键来完成。边界框是通过 ```DesiredSizeForSelection``` 依赖属性计算的（如果指定），否则使用 ```RenderSize``` 计算。

> 注意：可以通过将 ```IsSelected``` 依赖属性设置为 ```true``` 来编程选择容器。

如果左键没有释放并且鼠标移动，容器也会被选择，这将清除当前选择并在选定的容器上开始拖拽操作。

还可以通过在容器上释放右键来选择，右键点击会清除当前选择并选择目标容器。

根据选择容器时按下的 ```ModifierKeys``` 不同，会有不同的行为：
- ```Replace``` - 没有按下修饰键（默认行为，清除选定项并选择容器）
- ```Append``` - 按下 Shift 键（将容器添加到选定项）
- ```Invert``` - 按下 Ctrl 键（切换容器的 ```IsSelected``` 依赖属性）

只有在 ```IsSelectable``` 依赖属性为 ```true``` 时，```ItemContainer``` 才能被选择。

选择或取消选择容器会分别触发 ```Selected``` 和 ```Unselected``` 事件，并将 ```IsSelected``` 依赖属性设置为新值。

如果未重写默认样式，容器的边框将使用 ```SelectedBrush``` 依赖属性。

默认值：
- ```IsSelectable```: true
- ```IsSelected```: false

### 选择 API

* IsSelectableInArea

## 移动和拖拽

可以通过按住左键并移动鼠标来开始拖拽 ```ItemContainer``` 的操作。
只有在 ```IsDraggable``` 依赖属性设置为 ```true``` 时，```ItemContainer``` 才能被拖拽。

> 注意：可以通过设置它们的 ```Location``` 依赖属性来编程移动 ```ItemContainer```。

拖拽 ```ItemContainer``` 会触发一系列事件，这些事件由 ```NodifyEditor``` 处理并应用于所有选定的项：
- ```DragStarted``` - 将 ```IsPreviewingLocation``` 依赖属性设置为 ```true```;
- ```DragDelta``` - 在拖拽操作完成或取消之前持续触发，并将所有选定项移动；
- ```DragCompleted``` - 完成或取消拖拽操作，并将 ```Location``` 依赖属性设置为最终位置，```IsPreviewingLocation``` 设置为 ```false```。

> 注意：如果 ```AllowDraggingCancellation``` 设置为 ```true```，可以通过释放右键取消拖拽操作。

默认值：
- ```IsDraggable```: true
- ```AllowDraggingCancellation```: true
