﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <SolidColorBrush x:Key="ItemContainer.HighlightColor"
                     Color="DodgerBlue" />

    <DrawingBrush x:Key="ItemContainer.HighlightBrush"
                  Viewport="0 0 24 24"
                  ViewportUnits="Absolute"
                  TileMode="Tile">
        <DrawingBrush.Drawing>
            <DrawingGroup>
                <GeometryDrawing Brush="{StaticResource ItemContainer.HighlightColor}">
                    <GeometryDrawing.Geometry>
                        <GeometryGroup>
                            <RectangleGeometry Rect="0 0 50 50" />
                            <RectangleGeometry Rect="50 50 50 50" />
                        </GeometryGroup>
                    </GeometryDrawing.Geometry>
                </GeometryDrawing>
            </DrawingGroup>
        </DrawingBrush.Drawing>
    </DrawingBrush>

    <Style TargetType="{x:Type local:ItemContainer}">
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="SelectedBorderThickness"
                Value="2" />
        <Setter Property="BorderBrush"
                Value="DodgerBlue" />
        <Setter Property="SelectedBrush"
                Value="Orange" />
        <Setter Property="HighlightBrush"
                Value="{StaticResource ItemContainer.HighlightBrush}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:ItemContainer}">
                    <Border Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Padding="{TemplateBinding Padding}"
                            x:Name="Border"
                            CornerRadius="3">
                        <ContentPresenter />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected"
                               Value="True" />
                    <Condition Property="IsPreviewingSelection"
                               Value="{x:Null}" />
                </MultiTrigger.Conditions>
                <MultiTrigger.Setters>
                    <Setter Property="BorderBrush"
                            Value="{Binding SelectedBrush, RelativeSource={RelativeSource Self}}" />
                    <Setter Property="Margin"
                            Value="{Binding SelectedMargin, RelativeSource={RelativeSource Self}}" />
                    <Setter Property="BorderThickness"
                            Value="{Binding SelectedBorderThickness, RelativeSource={RelativeSource Self}}" />
                </MultiTrigger.Setters>
            </MultiTrigger>
            <Trigger Property="IsPreviewingSelection"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="{Binding SelectedBrush, RelativeSource={RelativeSource Self}}" />
                <Setter Property="Margin"
                        Value="{Binding SelectedMargin, RelativeSource={RelativeSource Self}}" />
                <Setter Property="BorderThickness"
                        Value="{Binding SelectedBorderThickness, RelativeSource={RelativeSource Self}}" />
            </Trigger>
            <Trigger Property="local:PendingConnection.IsOverElement"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="{Binding HighlightBrush, RelativeSource={RelativeSource Self}}" />
                <Setter Property="Margin"
                        Value="{Binding SelectedMargin, RelativeSource={RelativeSource Self}}" />
                <Setter Property="BorderThickness"
                        Value="{Binding SelectedBorderThickness, RelativeSource={RelativeSource Self}}" />
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>