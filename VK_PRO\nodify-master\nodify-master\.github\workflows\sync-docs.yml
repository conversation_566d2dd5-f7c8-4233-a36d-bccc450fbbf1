name: Documentation

on:
  push:
    branches:
      - master
    paths:
      - "docs/**"
  workflow_dispatch:

jobs:
  job-sync-docs-to-wiki:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v2
      - name: Sync docs to wiki
        uses: newrelic/wiki-sync-action@main
        with:
          source: docs
          destination: wiki
          token: ${{ secrets.DOCS_TOKEN  }}
