# GroupingMovementMode Enum  
  
**Namespace:** Nodify  
  
**Assembly:** Nodify  
  
**References:** [GroupingNode](Nodify_GroupingNode)  
  
Specifies the possible movement modes of a [GroupingNode](Nodify_GroupingNode).  
  
```csharp  
public enum GroupingMovementMode  
```  
  
## Fields  
  
### Group  
  
The [GroupingNode](Nodify_GroupingNode) will move its content when moved.  
  
```csharp  
Group = 0;  
```  
  
### Self  
  
The [GroupingNode](Nodify_GroupingNode) will not move its content when moved.  
  
```csharp  
Self = 1;  
```  
  
