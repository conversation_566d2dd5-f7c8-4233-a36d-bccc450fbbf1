﻿<Application x:Class="Nodify.Playground.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:nodify="https://miroiu.github.io/nodify"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary>
                    <Style x:Key="{x:Static SystemParameters.FocusVisualStyleKey}">
                        <Setter Property="Control.Template">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Rectangle StrokeThickness="1"
                                               StrokeDashArray="2"
                                               Margin="-2"
                                               RadiusX="3"
                                               RadiusY="3"
                                               Stroke="DodgerBlue" />
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </ResourceDictionary>
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/Nodify.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/FocusVisual.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Icons.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Shared;component/Themes/Nodify.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Nodify.Playground;component/Themes/Nodify.xaml" />
                <ResourceDictionary>
                    <Color x:Key="NodifyEditor.FocusVisualColor">DodgerBlue</Color>

                    <Style TargetType="{x:Type nodify:HotKeyControl}"
                           BasedOn="{StaticResource {x:Type nodify:HotKeyControl}}">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="{x:Type nodify:HotKeyControl}">
                                    <Border CornerRadius="3"
                                            Background="OrangeRed">
                                        <TextBlock Text="{Binding Number, RelativeSource={RelativeSource TemplatedParent}}"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   Foreground="White" />
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
