﻿<Window x:Class="Nodify.Playground.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Nodify.Playground"
        xmlns:shared="clr-namespace:Nodify;assembly=Nodify.Shared"
        xmlns:nodify="https://miroiu.github.io/nodify"
        mc:Ignorable="d"
        Title="MainWindow"
        Height="700"
        Width="1300">

    <Window.Resources>
        <shared:DebugConverter x:Key="DebugConverter" />
        <shared:ToStringConverter x:Key="ToStringConverter" />
        <shared:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
    </Window.Resources>

    <Window.DataContext>
        <local:PlaygroundViewModel />
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <local:NodifyEditorView x:Name="EditorView"
                                DataContext="{Binding GraphViewModel}"
                                Grid.RowSpan="3" />

        <!--ACTIONS-->
        <Border VerticalAlignment="Top"
                Background="{DynamicResource PanelBackgroundBrush}"
                Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <StackPanel Orientation="Horizontal">
                    <Button Command="{Binding GenerateRandomNodesCommand}"
                            Content="GENERATE NODES"
                            ToolTip="Generate nodes using the specified settings."
                            Style="{StaticResource HollowButton}" />
                    <Button Command="{Binding ToggleConnectionsCommand}"
                            Content="{Binding ConnectNodesText}"
                            ToolTip="Will add new connections if Connect Nodes is checked, otherwise it will disconnect nodes."
                            Style="{StaticResource HollowButton}" />
                    <Button Command="{Binding PerformanceTestCommand}"
                            Content="PERFORMANCE TEST"
                            ToolTip="You will encounter rendering performance issues. Try disabling the connections to see the difference."
                            Style="{StaticResource HollowButton}" />
                    <Button Command="{Binding ResetCommand}"
                            Content="RESET PLAYGROUND"
                            ToolTip="Reset the Location, Zoom, Nodes and Connections."
                            Style="{StaticResource HollowButton}" />
                    <Button Click="BringIntoView_Click"
                            Content="BRING INTO VIEW"
                            ToolTip="Bring a random node into view."
                            Style="{StaticResource HollowButton}" />
                    <Button Command="{x:Static nodify:EditorCommands.FitToScreen}"
                            Content="FIT TO SCREEN"
                            ToolTip="Scales the viewport to fit all nodes if that's possible."
                            CommandTarget="{Binding EditorInstance, ElementName=EditorView}"
                            Style="{StaticResource HollowButton}" />
                    <Button Command="{Binding GraphViewModel.CommentSelectionCommand}"
                            Content="COMMENT SELECTION"
                            ToolTip="Creates a comment node containing the selected nodes."
                            Style="{StaticResource HollowButton}" />
                    <Button Click="AnimateConnections_Click"
                            Content="TOGGLE CONNECTIONS ANIMATION"
                            ToolTip="Starts or stops animating the directional arrows on all connections (see DirectionalArrowsCount)"
                            Style="{StaticResource HollowButton}" />
                </StackPanel>

                <Button Style="{StaticResource IconButton}"
                        Content="{StaticResource ThemeIcon}"
                        Command="{Binding Source={x:Static shared:ThemeManager.SetNextThemeCommand}}"
                        ToolTip="Change theme"
                        Grid.Column="1" />
            </Grid>
        </Border>

        <!--SETTINGS-->
        <Expander Grid.Row="1"
                  HorizontalContentAlignment="Left"
                  VerticalContentAlignment="Center"
                  HorizontalAlignment="Left"
                  Background="{DynamicResource PanelBackgroundBrush}"
                  IsExpanded="True"
                  ExpandDirection="Left"
                  Padding="0 1 4 3">
            <Expander.Style>
                <Style TargetType="{x:Type Expander}"
                       BasedOn="{StaticResource {x:Type Expander}}">
                    <Setter Property="Tag"
                            Value="{StaticResource ExpandRightIcon}" />
                    <Style.Triggers>
                        <Trigger Property="IsExpanded"
                                 Value="True">
                            <Setter Property="Tag"
                                    Value="{StaticResource ExpandLeftIcon}" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Expander.Style>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <StackPanel Margin="0 0 5 10">
                    <TextBlock Margin="0 0 0 3">Search:</TextBlock>
                    <TextBox Text="{Binding Source={x:Static local:PlaygroundSettings.Instance}, Path=SearchText, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                             Padding="4" />
                </StackPanel>
                <ScrollViewer Grid.Row="1">
                    <Grid IsSharedSizeScope="True"
                          Width="330">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <Expander Header="Playground Settings"
                                  Padding="0 5 0 0"
                                  BorderThickness="0 0 0 1"
                                  IsExpanded="True"
                                  BorderBrush="{DynamicResource BackgroundBrush}">
                            <Expander.Style>
                                <Style TargetType="{x:Type Expander}"
                                       BasedOn="{StaticResource {x:Type Expander}}">
                                    <Setter Property="Tag"
                                            Value="{StaticResource ExpandRightIcon}" />
                                    <Style.Triggers>
                                        <Trigger Property="IsExpanded"
                                                 Value="True">
                                            <Setter Property="Tag"
                                                    Value="{StaticResource ExpandDownIcon}" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Expander.Style>
                            <Border BorderThickness="1"
                                    Padding="10"
                                    HorizontalAlignment="Stretch">
                                <local:SettingsView Items="{Binding Source={x:Static local:PlaygroundSettings.Instance}, Path=Settings}" />
                            </Border>
                        </Expander>

                        <Expander Header="Editor Settings"
                                  Padding="0 5 0 0"
                                  BorderThickness="0 0 0 1"
                                  IsExpanded="True"
                                  BorderBrush="{DynamicResource BackgroundBrush}"
                                  Grid.Row="1">
                            <Expander.Style>
                                <Style TargetType="{x:Type Expander}"
                                       BasedOn="{StaticResource {x:Type Expander}}">
                                    <Setter Property="Tag"
                                            Value="{StaticResource ExpandRightIcon}" />
                                    <Style.Triggers>
                                        <Trigger Property="IsExpanded"
                                                 Value="True">
                                            <Setter Property="Tag"
                                                    Value="{StaticResource ExpandDownIcon}" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Expander.Style>

                            <local:EditorSettingsView />
                        </Expander>
                    </Grid>
                </ScrollViewer>
            </Grid>
        </Expander>

        <!--INFORMATION-->
        <Border Grid.Row="2"
                Background="{DynamicResource PanelBackgroundBrush}"
                VerticalAlignment="Bottom"
                Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid.Resources>
                    <Style TargetType="{x:Type TextBlock}"
                           BasedOn="{StaticResource {x:Type TextBlock}}">
                        <Setter Property="Foreground"
                                Value="{DynamicResource ForegroundBrush}" />
                        <Setter Property="Margin"
                                Value="0 0 15 0" />
                    </Style>
                </Grid.Resources>

                <StackPanel Orientation="Horizontal">
                    <TextBlock ToolTip="The number of selected items.">
                        <TextBlock.Inlines>
                            <Run Text="Selected nodes: " />
                            <Run Foreground="YellowGreen"
                                 Text="{Binding GraphViewModel.SelectedNodes.Count, Mode=OneWay}" />
                            <Run Text="/" />
                            <Run Text="{Binding GraphViewModel.Nodes.Count, Mode=OneWay}" />
                        </TextBlock.Inlines>
                    </TextBlock>
                    <TextBlock ToolTip="The number of selected connections.">
                        <TextBlock.Inlines>
                            <Run Text="Selected connections: " />
                            <Run Foreground="YellowGreen"
                                 Text="{Binding GraphViewModel.SelectedConnections.Count, Mode=OneWay}" />
                            <Run Text="/" />
                            <Run Text="{Binding GraphViewModel.Connections.Count, Mode=OneWay}" />
                        </TextBlock.Inlines>
                    </TextBlock>

                    <Border Visibility="{Binding GraphViewModel.KeyboardNavigationLayer, Converter={StaticResource StringToVisibilityConverter}}"
                            ToolTip="Press CTRL+[ or CTRL+] in the editor to change the keyboard navigation layer."
                            Padding="14 0 0 0"
                            Height="16"
                            CornerRadius="3"
                            Background="{DynamicResource PanelBackgroundBrush}"
                            BorderThickness="1"
                            BorderBrush="{DynamicResource BorderBrush}">
                        <TextBlock>
                            <Run Text="Navigating: " />
                            <Run Foreground="{DynamicResource ForegroundBrush}"
                                 Text="{Binding GraphViewModel.KeyboardNavigationLayer, Mode=OneWay}" />
                        </TextBlock>
                    </Border>

                </StackPanel>

                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Right">
                    <TextBlock ToolTip="The viewport's location.">
                        <TextBlock.Inlines>
                            <Run Text="Location: " />
                            <Run Foreground="Orange"
                                 Text="{Binding Location.Value, Mode=OneWay, Converter={StaticResource ToStringConverter}, Source={x:Static local:EditorSettings.Instance}}" />
                        </TextBlock.Inlines>
                    </TextBlock>
                    <TextBlock ToolTip="The viewport's size.">
                        <TextBlock.Inlines>
                            <Run Text="Size: " />
                            <Run Foreground="YellowGreen"
                                 Text="{Binding GraphViewModel.ViewportSize, Mode=OneWay, Converter={StaticResource ToStringConverter}}" />
                        </TextBlock.Inlines>
                    </TextBlock>
                    <TextBlock ToolTip="The viewport's zoom. Not accurate when trying to zoom outside the MinViewportZoom and MaxViewportZoom because of dependency property coercion not updating the binding with the final result.">
                        <TextBlock.Inlines>
                            <Run Text="Zoom: " />
                            <Run Foreground="DodgerBlue"
                                 Text="{Binding Zoom, Mode=OneWay, Converter={StaticResource ToStringConverter}, Source={x:Static local:EditorSettings.Instance}}" />
                        </TextBlock.Inlines>
                    </TextBlock>
                    <TextBlock ToolTip="The estimated frame rate. (my be buggy)">
                        <TextBlock.Inlines>
                            <Run Text="FPS: " />
                            <Run Foreground="LawnGreen"
                                 Name="FPSText" />
                        </TextBlock.Inlines>
                    </TextBlock>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
