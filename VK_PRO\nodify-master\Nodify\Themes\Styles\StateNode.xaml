﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <Style TargetType="{x:Type local:StateNode}">
        <Setter Property="Padding"
                Value="7 5" />
        <Setter Property="BorderThickness"
                Value="10" />
        <Setter Property="BorderBrush"
                Value="#484848" />
        <Setter Property="Background"
                Value="#171717" />
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="HighlightBrush"
                Value="#D6D3D6" />
        <Setter Property="CornerRadius"
                Value="3" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:StateNode}">
                    <Border x:Name="Border"
                            Padding="{TemplateBinding BorderThickness}"
                            Margin="{TemplateBinding Margin}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border x:Name="PART_Content"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                Cursor="SizeAll"
                                MinWidth="30"
                                MinHeight="30"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <ContentPresenter />
                        </Border>
                        <Border.Style>
                            <Style TargetType="Border">
                                <Setter Property="Background"
                                        Value="{Binding BorderBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                                <Style.Triggers>
                                    <Trigger Property="IsMouseDirectlyOver"
                                             Value="True">
                                        <Setter Property="Background"
                                                Value="{Binding HighlightBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Border.Style>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="local:PendingConnection.IsOverElement"
                                 Value="True">
                            <Setter Property="Background"
                                    TargetName="Border"
                                    Value="{Binding HighlightBrush, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>