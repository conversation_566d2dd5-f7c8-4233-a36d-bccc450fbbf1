﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <local:SubtractConverter x:Key="SubtractConverter" />

    <SolidColorBrush x:Key="MinimapBackground"
                     Color="#121212"
                     Opacity="0.7" />
    <SolidColorBrush x:Key="MinimapItemBackground"
                     Color="DodgerBlue"
                     Opacity="0.8" />

    <Style x:Key="ViewportRectStyle"
           TargetType="Rectangle">
        <Setter Property="Stroke"
                Value="#74747c" />
        <Setter Property="StrokeThickness"
                Value="3" />
        <Setter Property="Fill">
            <Setter.Value>
                <SolidColorBrush Color="#74747c"
                                 Opacity="0.2" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:Minimap}">
        <Setter Property="Padding"
                Value="10" />
        <Setter Property="Background"
                Value="{StaticResource MinimapBackground}" />
        <Setter Property="ViewportStyle"
                Value="{StaticResource ViewportRectStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:Minimap}">
                    <Border Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="3"
                            ClipToBounds="True">
                        <Viewbox Stretch="Uniform"
                                 Margin="{TemplateBinding Padding}">
                            <Grid>
                                <local:MinimapPanel ViewportSize="{TemplateBinding ViewportSize}"
                                                    ViewportLocation="{TemplateBinding ViewportLocation}"
                                                    ResizeToViewport="{TemplateBinding ResizeToViewport}"
                                                    Extent="{Binding Extent, RelativeSource={RelativeSource TemplatedParent}, Mode=OneWayToSource}"
                                                    ItemsExtent="{Binding ItemsExtent, RelativeSource={RelativeSource TemplatedParent}, Mode=OneWayToSource}"
                                                    IsItemsHost="True"
                                                    x:Name="PART_ItemsHost" />
                                <Canvas>
                                    <Rectangle  Style="{TemplateBinding ViewportStyle}"
                                                Width="{Binding ViewportSize.Width, RelativeSource={RelativeSource TemplatedParent}}"
                                                Height="{Binding ViewportSize.Height, RelativeSource={RelativeSource TemplatedParent}}">
                                        <Canvas.Top>
                                            <MultiBinding Converter="{StaticResource SubtractConverter}">
                                                <Binding Path="ViewportLocation.Y"
                                                         RelativeSource="{RelativeSource TemplatedParent}" />
                                                <Binding Path="Extent.Location.Y"
                                                         RelativeSource="{RelativeSource TemplatedParent}" />
                                            </MultiBinding>
                                        </Canvas.Top>
                                        <Canvas.Left>
                                            <MultiBinding Converter="{StaticResource SubtractConverter}">
                                                <Binding Path="ViewportLocation.X"
                                                         RelativeSource="{RelativeSource TemplatedParent}" />
                                                <Binding Path="Extent.Location.X"
                                                         RelativeSource="{RelativeSource TemplatedParent}" />
                                            </MultiBinding>
                                        </Canvas.Left>
                                    </Rectangle>
                                </Canvas>
                            </Grid>
                        </Viewbox>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type local:MinimapItem}">
        <Setter Property="Background"
                Value="{StaticResource MinimapItemBackground}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:MinimapItem}">
                    <Border Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="3">
                        <ContentPresenter />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>