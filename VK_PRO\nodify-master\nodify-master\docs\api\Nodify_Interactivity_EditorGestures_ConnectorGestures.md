# EditorGestures.ConnectorGestures Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [EditorGestures.ConnectorGestures](Nodify_Interactivity_EditorGestures_ConnectorGestures)  
  
**References:** [EditorGestures](Nodify_Interactivity_EditorGestures), [InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
```csharp  
public class ConnectorGestures  
```  
  
## Constructors  
  
### EditorGestures.ConnectorGestures()  
  
```csharp  
public ConnectorGestures();  
```  
  
## Properties  
  
### CancelAction  
  
```csharp  
public InputGestureRef CancelAction { get; set; }  
```  
  
**Property Value**  
  
[InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
### Connect  
  
```csharp  
public InputGestureRef Connect { get; set; }  
```  
  
**Property Value**  
  
[InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
### Disconnect  
  
```csharp  
public InputGestureRef Disconnect { get; set; }  
```  
  
**Property Value**  
  
[InputGestureRef](Nodify_Interactivity_InputGestureRef)  
  
## Methods  
  
### Apply(EditorGestures.ConnectorGestures)  
  
```csharp  
public void Apply(EditorGestures.ConnectorGestures gestures);  
```  
  
**Parameters**  
  
`gestures` [EditorGestures.ConnectorGestures](Nodify_Interactivity_EditorGestures_ConnectorGestures)  
  
### Unbind()  
  
```csharp  
public void Unbind();  
```  
  
