## 目录
* [移动视口](#平移)
* [缩放](#缩放)
* [选择项目](#选择)
  * [选择功能相关API](#选择功能相关API)
* [对齐到轴网](#对齐)
* [命令](#命令)
* [编辑器API](#编辑器API)

## 平移

平移是通过按住鼠标右键并移动鼠标来完成的，可以通过将```DisablePanning```依赖属性设置为```true```来禁用平移功能。
> 注意：可以通过设置```ViewportLocation```依赖属性以编程方式更改它。

在平移过程中，```IsPanning```依赖属性将被设置为```true```，并且```ViewportSize```, ```ViewportLocation``` 和 ```ViewportTransform``` 依赖属性将会更新。

默认情况下，自动平移也是启用的，可以通过将```DisableAutoPanning```依赖属性设置为```true```来禁用。其行为是在选择或拖动选区或待连接物体靠近视口边缘时平移视口。

可以使用```AutoPanSpeed```依赖属性更改自动平移速度，使用```AutoPanEdgeDistance```依赖属性更改触发平移的边缘距离。

平移功能还可以与选择和缩放结合使用，而自动平移不仅可以与选择和缩放结合使用，还可以与拖动选区或预备连接一起使用。

默认值：

* ```DisablePanning```: false
* ```DisableAutoPanning```: false
* ```AutoPanSpeed```: 10 pixels per tick
* ```AutoPanEdgeDistance```: 15 pixels
* ```AutoPanningTickRate```: 1 millisecond

## 缩放 

缩放是通过使用鼠标滚轮或按```CTRL +```来放大或按```CTRL -```来缩小完成的，并且可以通过将```DisableZooming```依赖属性设置为```true```来禁用缩放功能。
> 注意：可以通过将```ViewportZoom```依赖属性设置为```MinViewportZoom```和```MaxViewportZoom```之间的值来以编程方式更改它。

在缩放过程中，```ViewportLocation```, ```ViewportSize``` 和 ```ViewportTransform``` 依赖属性将会更新。

缩放功能还可以与平移、拖动选区或预备连接结合使用。

默认值：
* ```ViewportZoom```: 1
* ```MinViewportZoom```: 0.1
* ```MaxViewportZoom```: 2

## 选择

选择项目是通过按住鼠标左键并移动鼠标来完成的。当选择操作正在进行时，```IsSelecting```依赖属性将被设置为```true```，并且```SelectedArea```依赖属性将随着每次移动而更新。
> 注意：也可以通过将集合绑定到```SelectedItems```依赖属性来以编程方式设置选定的项目。

如果启用了实时选择（```EnableRealtimeSelection```: true），则在调整选择矩形大小时，项目将被选中和取消选中。否则，在选择操作完成后，才会选中```SelectedArea```中包含的项目。

当选择一个```ItemContainer```时，其```IsSelected```依赖属性将被设置为```true```。

根据开始选择时按住的```ModifierKeys```不同，使用不同的行为：
- ```Replace``` - 无修饰键（默认行为，清除已选项目并开始新选择）
- ```Append``` - shift键（将选择添加到当前已选项目）
- ```Remove``` - alt键（从当前已选项目中移除选择）
- ```Invert``` - control键（移除选定项目并添加未选中项目）

选择项目也可以与平移和缩放结合使用。

默认值:
* ```EnableRealtimeSelection```: true

### 选择功能相关API:

以下方法可以在 NodifyEditor 实例上调用。

* SelectArea
* InvertSelection
* UnselectArea

## 对齐

当移动选中项目时，```GridCellSize```依赖属性会决定将选定项目对齐到何处。
对齐是相对于选定项目的位置而不是虚拟网格的位置。

如果选定项目在初始创建时未对齐到网格，或者在运行时更改了```GridCellSize```，那么当```EnableSnappingCorrection```依赖属性为```true```时，则在移动选中项目后将会校正最终位置。

默认值:
* ```GridCellSize```: 1
* ```EnableSnappingCorrection```: true

## 命令

 在```EditorCommands```类中可以看到以下```RoutedUICommand```：
* ```ZoomIn``` - ```CTRL +```（相对于视口中心放大）
* ```ZoomOut``` - ```CTRL -```（相对于视口中心缩小）
* ```SelectAll``` - ```CTRL A```（选择所有项目）
* ```BringIntoView``` - 将视口移动到指定位置，默认为[0,0]。（```CommandParameter```为类型为```Point```或```string```的位置）
* ```Align``` - 使用指定的对齐方法对齐选定的项目，默认为顶部。（```CommandParameter```为类型为```Alignment```或```string```的对齐方法。可能的对齐方法：Top、Left、Bottom、Right、Middle、Center）
* ```FitToScreen``` - 缩放并移动```Viewport```以显示尽可能多的项目

## 编辑器API

您可以在`NodifyEditor`实例上以编程方式调用这些命令的相应方法。

* FitToScreen
* BringIntoView
* ZoomAtPosition
* ZoomIn
* ZoomOut