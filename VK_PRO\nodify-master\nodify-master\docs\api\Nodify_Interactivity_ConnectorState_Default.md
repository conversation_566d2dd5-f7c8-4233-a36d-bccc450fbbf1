# ConnectorState.Default Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<Connector\>](Nodify_Interactivity_InputElementState_TElement_) → [ConnectorState.Default](Nodify_Interactivity_ConnectorState_Default)  
  
**References:** [Connector](Nodify_Connector)  
  
```csharp  
public class Default : InputElementState<Connector>  
```  
  
## Constructors  
  
### ConnectorState.Default(Connector)  
  
```csharp  
public Default(Connector connector);  
```  
  
**Parameters**  
  
`connector` [Connector](Nodify_Connector)  
  
## Methods  
  
### OnMouseDown(MouseButtonEventArgs)  
  
```csharp  
protected override void OnMouseDown(MouseButtonEventArgs e);  
```  
  
**Parameters**  
  
`e` [<PERSON><PERSON><PERSON>onEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.MouseButtonEventArgs)  
  
