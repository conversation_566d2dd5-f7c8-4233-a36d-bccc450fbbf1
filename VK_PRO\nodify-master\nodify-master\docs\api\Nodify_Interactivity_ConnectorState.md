# ConnectorState Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [ConnectorState](Nodify_Interactivity_ConnectorState)  
  
```csharp  
public static class ConnectorState  
```  
  
## Properties  
  
### EnableToggledConnectingMode  
  
Determines whether toggled connecting mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledConnectingMode { get; set; }  
```  
  
**Property Value**  
  
[<PERSON><PERSON>an](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
