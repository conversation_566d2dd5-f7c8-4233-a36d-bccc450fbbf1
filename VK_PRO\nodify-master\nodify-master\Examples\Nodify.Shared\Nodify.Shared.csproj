﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

  <PropertyGroup>
    <UseWPF>true</UseWPF>
    <TargetFrameworks>net9-windows;net8-windows;net6-windows;net5-windows;netcoreapp3.1;net48;net472</TargetFrameworks>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetFramework)'=='net472' OR '$(TargetFramework)'=='net48'">
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Page Update="Themes\Controls.xaml">
      <SubType>Designer</SubType>
    </Page>
    <Page Update="Themes\Generic.xaml">
      <SubType>Designer</SubType>
    </Page>
    <Page Update="Themes\Icons.xaml">
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>

</Project>