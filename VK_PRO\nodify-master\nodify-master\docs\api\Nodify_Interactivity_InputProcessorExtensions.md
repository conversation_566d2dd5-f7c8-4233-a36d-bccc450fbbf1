# InputProcessorExtensions Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputProcessorExtensions](Nodify_Interactivity_InputProcessorExtensions)  
  
**References:** [InputProcessor](Nodify_Interactivity_InputProcessor)  
  
Provides extension methods for the [InputProcessor](Nodify_Interactivity_InputProcessor) class.  
  
```csharp  
public static class InputProcessorExtensions  
```  
  
## Methods  
  
### AddSharedHandlers(InputProcessor, TElement)  
  
```csharp  
public static void AddSharedHandlers<TElement>(InputProcessor inputProcessor, TElement instance);  
```  
  
**Parameters**  
  
`inputProcessor` [InputProcessor](Nodify_Interactivity_InputProcessor)  
  
`instance` [TElement](Nodify_Interactivity_InputProcessorExtensions_TElement)  
  
