## 目录

- [目录](#目录)
- [移动视口](#移动视口)
- [缩放](#缩放)
- [自定义外观](#自定义外观)

`Minimap` 控件是一个自定义控件，旨在为 `NodifyEditor` 提供同步的缩略视图。
它继承自 `ItemsControl`，通过 `ItemsSource` 属性显示项目。每个项目都包装在 `MinimapItem` 容器中，需在 `ItemContainerStyle` 中设置：


> [!TIP]
> 若希望在小地图中实时移动节点，需要将 `NodifyEditor.EnableDraggingContainersOptimizations` 设置为 `false`。

此外，该控件还会显示一个表示编辑器可见区域的视口矩形，需要设置 `ViewportLocation` 和 `ViewportSize` 属性。

```xml
<nodify:Minimap
    ItemsSource="{Binding ItemsSource, ElementName=Editor}"
    ViewportLocation="{Binding ViewportLocation, ElementName=Editor}"
    ViewportSize="{Binding ViewportSize, ElementName=Editor}"
    Zoom="OnMinimapZoom">
    <nodify:Minimap.ItemContainerStyle>
        <Style TargetType="nodify:MinimapItem">
            <Setter Property="Location" Value="{Binding MyItemLocation}" />
        </Style>
    </nodify:Minimap.ItemContainerStyle>
</nodify:Minimap>
```

```csharp
private void OnMinimapZoom(object sender, ZoomEventArgs e)
{
    Editor.ZoomAtPosition(e.Zoom, e.Location);
}
```

> [!IMPORTANT]
> `Width` 和 `Height` 应由父容器限制，或在 `Minimap` 上设置为常量值，以防止其根据内容大小改变。

## 移动视口

按住鼠标点击并拖动即可移动视口（平移）。若将 `IsReadOnly` 属性设置为 `true`，则无法移动。

在平移过程中，`ViewportLocation` 会更新，因此需要进行双向绑定（默认即为双向绑定）。

平移手势可通过设置 `EditorGestures.Mappings.Minimap.DragViewport` 为所需手势进行配置。

## 缩放

滚动鼠标滚轮即可进行缩放。若将 `IsReadOnly` 属性设置为 `true` 或未处理 `Zoom` 事件，则无法缩放。


缩放时的修饰键可通过设置 `EditorGestures.Mappings.Minimap.ZoomModifierKey` 为所需值进行配置。

## 自定义外观

可使用 `ViewportStyle` 自定义视口矩形的样式，例如：

```xml
<Style x:Key="MyViewportStyle" TargetType="Rectangle">
    <Setter Property="Fill" Value="Transparent"/>
    <Setter Property="Stroke" Value="White"/>
    <Setter Property="StrokeThickness" Value="3"/>
</Style>

<nodify:Minimap ViewportStyle="{StaticResource MyViewportStyle}" ... />
```

`MaxViewportOffset` 属性用于限制在 [移动视口](#移动视口) 时，视口可偏离项目的最大距离。

`ResizeToViewport` 属性用于改变小地图的缩放行为。
若设置为 `true`，则小地图会始终与项目一起缩放，以显示视口。

![scale-with-viewport](https://github.com/user-attachments/assets/7a8887bf-f3f4-44d7-8311-6d9ba7869d78)

如果设置为 `false`，则小地图仅显示项目，视口可以超出项目范围。

![viewport-out-of-bounds](https://github.com/user-attachments/assets/5d3b388e-8e40-4bfe-af3b-4c12fb47548d)
