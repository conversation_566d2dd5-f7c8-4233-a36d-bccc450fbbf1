# MinimapState Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [MinimapState](Nodify_Interactivity_MinimapState)  
  
```csharp  
public static class MinimapState  
```  
  
## Properties  
  
### EnableToggledPanningMode  
  
Determines whether toggled panning mode is enabled, allowing the user to start and end the interaction in two steps with the same input gesture.  
  
```csharp  
public static bool EnableToggledPanningMode { get; set; }  
```  
  
**Property Value**  
  
[Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean)  
  
