# AllGestures Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputGesture](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.InputGesture) → [MultiGesture](Nodify_Interactivity_MultiGesture) → [AllGestures](Nodify_Interactivity_AllGestures)  
  
```csharp  
public sealed class AllGestures : MultiGesture  
```  
  
## Constructors  
  
### AllGestures(InputGesture[])  
  
```csharp  
public AllGestures(InputGesture[] gestures);  
```  
  
**Parameters**  
  
`gestures` [InputGesture[]](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.InputGesture[])  
  
