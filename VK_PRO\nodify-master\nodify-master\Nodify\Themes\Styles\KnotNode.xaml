﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <DataTemplate x:Key="DefaultConnectorTemplate">
        <local:Connector Cursor="Arrow" />
    </DataTemplate>

    <Style TargetType="{x:Type local:KnotNode}">
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Foreground"
                Value="DodgerBlue" />
        <Setter Property="BorderBrush"
                Value="Transparent" />
        <Setter Property="Padding"
                Value="15 5" />
        <Setter Property="Cursor"
                Value="SizeAll" />
        <Setter Property="ContentTemplate"
                Value="{StaticResource DefaultConnectorTemplate}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:KnotNode}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="3">
                        <ContentPresenter />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>