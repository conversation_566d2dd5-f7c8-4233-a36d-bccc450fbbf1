
#### Q: Why is the connection not following the connectors when I move a node?

A1: You didn't bind the `Anchor` of the connector, or the `Source` and `Target` properties of the connection.

A2: The `Anchor` of the connectors updates only if the `IsConnected` property is set to true.

***

#### Q: Can I change the mouse/key bindings?

A: Yes! You can configure the [editor gestures](https://github.com/miroiu/nodify/blob/master/Nodify/EditorGestures.cs) to your liking.

***

#### Q: Would there be an Avalonia port?

A: There is a fantastic Avalonia port available! You can check it out [here](https://github.com/BAndysc/nodify-avalonia). Huge thanks to [BAndysc](https://github.com/BAndysc) who made this possible.

***

#### Q: Is there a non-MVVM approach to adding nodes and connections?

A: No.

***

#### Q: How can I set the selected nodes to be always on top?

A: https://github.com/miroiu/nodify/issues/57

***

#### Q: How can I change the size of a node?

A: https://github.com/miroiu/nodify/issues/55

***

#### Q: How can I limit the X and Y panning?

A: https://github.com/miroiu/nodify/issues/53

***

#### Q: The viewport is lagging. How can I fix it?

A: https://github.com/miroiu/nodify/issues/60

***

#### Q: How can I drag nodes from a toolbox into the editor?

A: https://github.com/miroiu/nodify/issues/81

***

#### Q: How can I create a custom connection?

A: https://github.com/miroiu/nodify/issues/121

***

#### Q: How can I create a custom node?

A: https://github.com/miroiu/nodify/discussions/234

***

#### Q: How can I make the editor read-only?

A: https://github.com/miroiu/nodify/issues/206

***

#### Q: Is there an option for auto layout?

A:  https://github.com/miroiu/nodify/issues/73

