﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Nodify">

    <Style TargetType="{x:Type local:BaseConnection}">
        <Style.Triggers>
            <Trigger Property="local:CuttingLine.IsOverElement"
                     Value="True">
                <Setter Property="Opacity"
                        Value="0.4" />
            </Trigger>
            <Trigger Property="IsSelectable"
                     Value="True">
                <Setter Property="Cursor"
                        Value="Hand" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="{x:Type local:Connection}"
           BasedOn="{StaticResource {x:Type local:BaseConnection}}">
        <Setter Property="StrokeThickness"
                Value="3" />
        <Setter Property="Stroke"
                Value="DodgerBlue" />
        <Setter Property="Fill"
                Value="DodgerBlue" />
        <Setter Property="Spacing"
                Value="20" />
    </Style>

    <Style TargetType="{x:Type local:LineConnection}"
           BasedOn="{StaticResource {x:Type local:BaseConnection}}">
        <Setter Property="StrokeThickness"
                Value="3" />
        <Setter Property="Stroke"
                Value="DodgerBlue" />
        <Setter Property="Fill"
                Value="DodgerBlue" />
        <Setter Property="Spacing"
                Value="30" />
    </Style>

    <Style TargetType="{x:Type local:CircuitConnection}"
           BasedOn="{StaticResource {x:Type local:BaseConnection}}">
        <Setter Property="StrokeThickness"
                Value="3" />
        <Setter Property="Stroke"
                Value="DodgerBlue" />
        <Setter Property="Fill"
                Value="DodgerBlue" />
        <Setter Property="Spacing"
                Value="30" />
    </Style>

    <Style TargetType="{x:Type local:StepConnection}"
           BasedOn="{StaticResource {x:Type local:BaseConnection}}">
        <Setter Property="StrokeThickness"
                Value="3" />
        <Setter Property="Stroke"
                Value="DodgerBlue" />
        <Setter Property="Fill"
                Value="DodgerBlue" />
        <Setter Property="Spacing"
                Value="30" />
    </Style>

</ResourceDictionary>