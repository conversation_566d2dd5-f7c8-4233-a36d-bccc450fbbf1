# ContainerState.Default Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementStateStack\<ItemContainer\>](Nodify_Interactivity_InputElementStateStack_TElement_) → [ContainerState.Default](Nodify_Interactivity_ContainerState_Default)  
  
**References:** [ItemContainer](Nodify_ItemContainer)  
  
```csharp  
public sealed class Default : InputElementStateStack<ItemContainer>  
```  
  
## Constructors  
  
### ContainerState.Default(ItemContainer)  
  
```csharp  
public Default(ItemContainer container);  
```  
  
**Parameters**  
  
`container` [ItemContainer](Nodify_ItemContainer)  
  
