# IKeyboardFocusTarget\<TElement\> Interface  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
```csharp  
public interface IKeyboardFocusTarget<TElement>  
```  
  
## Properties  
  
### Bounds  
  
```csharp  
public virtual Rect Bounds { get; set; }  
```  
  
**Property Value**  
  
[Rect](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Rect)  
  
### Element  
  
```csharp  
public virtual TElement Element { get; set; }  
```  
  
**Property Value**  
  
[TElement](Nodify_Interactivity_IKeyboardFocusTarget_TElement__TElement)  
  
