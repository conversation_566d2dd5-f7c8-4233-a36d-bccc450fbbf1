# MinimapState.KeyboardNavigation Class  
  
**Namespace:** Nodify.Interactivity  
  
**Assembly:** Nodify  
  
**Inheritance:** [Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object) → [InputElementState\<Minimap\>](Nodify_Interactivity_InputElementState_TElement_) → [MinimapState.KeyboardNavigation](Nodify_Interactivity_MinimapState_KeyboardNavigation)  
  
**References:** [Minimap](Nodify_Minimap)  
  
```csharp  
public class KeyboardNavigation : InputElementState<Minimap>  
```  
  
## Constructors  
  
### MinimapState.KeyboardNavigation(Minimap)  
  
```csharp  
public KeyboardNavigation(Minimap element);  
```  
  
**Parameters**  
  
`element` [Minimap](Nodify_Minimap)  
  
## Methods  
  
### OnKeyDown(KeyEventArgs)  
  
```csharp  
protected override void OnKeyDown(KeyEventArgs e);  
```  
  
**Parameters**  
  
`e` [KeyEventArgs](https://docs.microsoft.com/en-us/dotnet/api/System.Windows.Input.KeyEventArgs)  
  
