The ```ItemContainer``` is the **most important** piece of an editor. It is a content control that wraps every other control that is generated by the ```NodifyEditor```'s ```ItemsSource``` and has a `Location` in graph coordinates. 

## Table of contents
 - [Selecting](#selecting)
   - [API](#selection-api)
 - [Moving](#moving-and-dragging)

## Selecting

Selecting an ```ItemContainer``` is done by releasing the left mouse button over the bounding box of the container. The bounding box is calculated using the ```DesiredSizeForSelection``` dependency property if specified, otherwise using the ```RenderSize```. 

> Note: A container can be selected programmatically by setting the ```IsSelected``` dependency property to ```true```.

The container will also be selected if the left mouse button is not released and the mouse is moved, which will clear the current selection and start a dragging operation on the selected container.

Selection can also be done by releasing the right mouse button on the container which will clear the current selection and select the target container.

Different behavior is used depending on the ```ModifierKeys``` held when selecting a container:
- ```Replace``` - no modifier key (default behavior, clears the selected items and selects the container)
- ```Append``` - shift key (adds the container to the selected items)
- ```Invert``` - control key (toggles the container's ```IsSelected``` dependency property)

An ```ItemContainer``` can only be selected if the ```IsSelectable``` dependency property is ```true```.

Selecting or unselecting a container will fire the ```Selected``` event, respectively the ```Unselected``` event, and will set the ```IsSelected``` dependency property to the new value.

If the default style is not overridden, the container's border will use the ```SelectedBrush``` dependency property.

Default values:
- ```IsSelectable```: true
- ```IsSelected```: false

### Selection API

* IsSelectableInArea

## Moving and dragging

A dragging operation can be started by holding the left mouse button on an ```ItemContainer``` and moving the mouse.
An ```ItemContainer``` can be dragged only if its ```IsDraggable``` dependency property is set to ```true```.

> Note: ```ItemContainer```s can be programmatically moved by setting their ```Location``` dependency property.

Dragging an ```ItemContainer``` fires a sequence of events that are handled by the ```NodifyEditor``` and applies to all the selected items:
- ```DragStarted``` - will set ```IsPreviewingLocation``` dependency property to ```true```;
- ```DragDelta``` - will be fired until the dragging operation is finished or canceled and will move all the selected items with it;
- ```DragCompleted``` - will finish or cancel the dragging operation and set the ```Location``` dependency property to the final position and ```IsPreviewingLocation``` to ```false```.

> Note: A dragging operation can be canceled by releasing the right mouse button if ```AllowDraggingCancellation``` is set to ```true```.

Default values:
- ```IsDraggable```: true
- ```AllowDraggingCancellation```: true
